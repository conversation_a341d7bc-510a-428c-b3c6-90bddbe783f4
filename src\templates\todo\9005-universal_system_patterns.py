#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "9005-a-universal_system_pattern": {
        "title": "Universal System Pattern Crystallizer",
        "interpretation": "Your goal is not to demonstrate patterns superficially, but to crystallize their indivisible essence and operational logic. Execute as pattern architect:",
        "transformation": "`{role=pattern_architect; input=null; process=[distill_interpretation_logic(), formalize_transformation_mechanics(), reveal_pattern_genesis()]; output={universal_pattern:dict}}`",
        "context": {
            "interpretation_pattern": {
                "purpose": "To create transformative intention by establishing a clear, focused, and specialized directive.",
                "atomic_structure": "Your goal is not to [1] but to [2]. [3]. Execute as [4]:",
                "components": {
                    "[1]_negation_clause": {
                        "function": "Prohibits default or naive behavior to prevent common failure modes.",
                        "rule": "Must use a strong verb + object (e.g., 'answer questions', 'summarize content')."
                    },
                    "[2]_affirmation_clause": {
                        "function": "Mandates a specific, value-driven transformation.",
                        "rule": "Must use a precise, directional verb + object (e.g., 'detonate complexity', 'synthesize essence')."
                    },
                    "[3]_operational_directive": {
                        "function": "Specifies execution parameters, clarifying the scope and constraints.",
                        "rule": "A concise sentence defining the 'how' or 'why' of the transformation."
                    },
                    "[4]_role_embodiment": {
                        "function": "Assigns a specialized operational identity to the process.",
                        "rule": "Follows a 'domain_function' snake_case pattern (e.g., 'essence_amplifier')."
                    }
                },
                "perfect_realization": "Your goal is not to describe patterns but to manifest their purest form through atomic decomposition and flawless recombination. Execute as pattern_crystallizer:"
            },
            "transformation_pattern": {
                "purpose": "To execute the transformative intention with mechanical precision and verifiable quality.",
                "atomic_structure": "`{role=...; input=[...]; process=[...]; constraints=[...]; requirements=[...]; output={...}}`",
                "components": {
                    "role": {
                        "function": "Defines the specialized operational identity, matching the interpretation.",
                        "rule": "A single, descriptive snake_case string."
                    },
                    "input": {
                        "function": "Declares the explicit data contract for the operation.",
                        "rule": "An array of parameters, each with a name and type (e.g., [raw_text:str])."
                    },
                    "process": {
                        "function": "Lists the ordered, atomic steps of the transformation.",
                        "rule": "An array of imperative function calls, like 'extract_core()'."
                    },
                    "constraints": {
                        "function": "Sets inviolable boundaries that the process must not cross.",
                        "rule": "An array of present-tense declarations, like 'preserve_intent()'."
                    },
                    "requirements": {
                        "function": "Defines the non-negotiable quality standards for the output.",
                        "rule": "An array of benefit-oriented standards, like 'maximal_clarity'."
                    },
                    "output": {
                        "function": "Specifies the precise structure and type of the final result.",
                        "rule": "A dictionary literal defining the output schema, like {enhanced_text:str}."
                    }
                },
                "perfect_realization": "`{role=essence_refiner; input=[raw_concept:any]; process=[isolate_core_meaning(), eliminate_redundancies()]; constraints=[preserve_original_intent()]; requirements=[achieve_conceptual_density()]; output={refined_essence:str}}`"
            },
            "pattern_genesis": {
                "purpose": "To ground the system in first principles, moving beyond arbitrary rules.",
                "interpretation_origin": "Derived from cognitive principles: Negation creates focus, Affirmation provides direction, and Specialization enables mastery.",
                "transformation_origin": "Derived from information theory: A structured process with clear inputs, outputs, and constraints ensures deterministic, high-fidelity transformations."
            }
        }
    },

}
def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
