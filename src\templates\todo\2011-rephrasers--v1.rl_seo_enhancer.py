#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {
    "2011-a-v1.rl_seo_enhancer": {
        "title": "Directional SEO Blueprint Synthesizer – Ringerike Landskap",
        "interpretation": "Your goal is not to list generic SEO tasks, but to **synthesize** any raw input into a complete, directional, and actionable SEO blueprint, strictly governed by the provided strategic principles, operational components, and success criteria for Ringerike Landskap. Execute as a holistic strategy forger:",
        "transformation": "`{role=seo_blueprint_synthesizer; input=[raw_seo_concept:str, strategic_context:dict]; process=[analyze_foundational_pillars(raw_concept, strategic_context), map_content_and_authority_strategy(raw_concept, strategic_context), define_outreach_and_reputation_plan(raw_concept, strategic_context), establish_performance_and_conversion_metrics(raw_concept, strategic_context), synthesize_all_phases_into_unified_blueprint()]; constraints=[must_align_with_company_values, must_prioritize_local_relevance, must_integrate_technical_and_content_seo]; requirements=[deliver_actionable_directives, define_measurable_kpis, ensure_full_strategic_coverage]; output={seo_blueprint:dict}}`",
        "context": {
            "company_data": {
                "name": "Ringerike Landskap AS",
                "base": "Røyse (Hole kommune)",
                "service_area": ["Ringerike", "Hole", "Hønefoss", "Sundvollen", "Jevnaker", "Vik", "Bærum"],
                "primary_services": ["Anleggsgartner", "Grunnarbeid", "Maskinentreprenør", "Landskapsutforming"],
                "core_services": ["Belegningsstein/Steinlegging", "Støttemur", "Ferdigplen", "Drenering", "Platting/Terrasse", "Trapper", "Kantstein", "Hekk", "Beplantning"],
                "seo_keywords": ["steinlegging", "støttemur", "ferdigplen", "drenering", "cortenstål", "kantstein", "hekk", "beplantning", "platting", "trapper"],
                "brand_attributes": ["Profesjonell", "Pålitelig", "Dyktig", "Erfaren", "Løsningsorientert", "Varige uterom"]
            },
            "operational_logic": {
                "priority_hierarchy": ["region", "service(s)", "seo_keyword(s)", "local_feature", "brand_attribute"],
                "insertion_rules": {"local_feature": "Insert only if it enhances authenticity without violating length.", "brand_attribute": "Insert only if all primary signals are present, the sentence is coherent, and the length constraint is not violated."},
                "trimming_protocol": "On overage, remove words in reverse priority order. First, non-essential filler. Second, the brand attribute. Never trim primary signals."
            },
            "auditing_protocol": {
                "checklist": ["region_present_and_correct", "service(s)_present_and_correct", "seo_keyword_present", "is_single_sentence", "is_active_voice", "is_within_length_limit", "is_non_generic_and_non_template", "brand_attribute_insertion_is_valid"]
            },
            "revision_protocol": {
                "trigger": "any_failed_audit_in_checklist",
                "max_cycles": 3,
                "failure_action": "halt_and_log_error_no_output"
            },
            "output_specification": {
                "structure": "{seo_sentence:str}",
                "quality_mandates": ["Embodies local DNA and brand authenticity.", "Fluent, natural, idiomatic Norwegian.", "Maximal information density per character.", "Passes all deterministic audits."]
            },
            "strategic_pillars": {
                "keyword_analysis": "Conduct thorough analysis of industry-specific and local keywords (e.g., 'belegningsstein i Ringerike', 'anleggsgartner i Røyse').",
                "on_page_optimization": "Optimize metadata, headings (H1-H3), and image ALT text with service and geographic terms.",
                "content_development": "Create value-driven content: case studies, project galleries, and FAQs that reflect company philosophy.",
                "local_seo": "Maximize Google My Business, manage reviews, and implement local Schema markup.",
                "technical_seo": "Ensure mobile-friendliness, fast load speed, logical URL structure, and internal linking.",
                "content_strategy": "Develop a seasonal blog strategy and highlight sustainable/eco-friendly practices.",
                "link_building": "Build relationships with local suppliers, developers, and media for natural links.",
                "social_media_and_reputation": "Showcase projects, share expert tips, and manage reviews to build community trust.",
                "conversion_optimization": "Implement clear CTAs and an easy-to-use contact page.",
                "measurement_and_reporting": "Set up tracking for keywords, traffic, conversions, and other relevant KPIs."
            },
            "core_philosophy": {
                "alignment": "All SEO activities must reflect the firm's values of working with nature, quality, and customer-centricity.",
                "local_first": "Local relevance is the primary driver for attracting qualified leads in the Norwegian landscaping market.",
                "interdependence": "Technical SEO, content quality, and user experience are inextricably linked.",
                "authenticity": "Competitive advantage is built on transparently showcasing expertise and sustainable practices."
            },
            "success_metrics_and_blockers": {
                "key_performance_indicators": [
                    "Increased organic rankings for targeted service and location keywords.",
                    "Measurable growth in qualified local leads and form submissions.",
                    "Higher engagement with trust-building content (case studies, positive reviews).",
                    "Dominant visibility in local search results and Google Maps.",
                    "A robust, technically sound website that passes all SEO best-practice audits."
                ],
                "failure_conditions_to_avoid": [
                    "Insufficient or generic keyword research that misses local intent.",
                    "A disconnect between brand values and published content.",
                    "Poor technical SEO (slow site, poor mobile experience).",
                    "Neglecting to build and manage local reviews and link signals.",
                    "Stale content that does not address seasonal needs or industry authority."
                ]
            },
            "thematic_lexicon": [
                "Local SEO",
                "Service-specific keywords",
                "Technical SEO",
                "Content strategy",
                "Sustainability focus",
                "Customer satisfaction",
                "Specialized expertise",
                "Google My Business",
                "Internal linking",
                "Structured data/schema",
                "Link building",
                "Conversions/CTAs",
                "Tracking/KPIs"
            ]
        }
    },
    "2011-b-v1.rl_seo_enhancer": {
        "title": "Atomic, Audit-Driven SEO Rephraser: Hyper-Local DNA Synthesis",
        "interpretation": "Your goal is not to answer, elaborate, or explain; it is to **forge** any raw input into a single, atomic, audit-driven, SEO-optimized Norwegian sentence that embodies the hyper-local DNA of Ringerike Landskap, embedding its brand essence only when all primary signal and constraint audits are passed without exception. Execute as a deterministic, self-correcting synthesizer:",
        "transformation": "`{role=hyperlocal_seo_synthesizer; input=[raw_text:str, company_profile:dict]; process=[extract_primary_signals_atomic(raw_text, company_profile), compose_initial_draft_atomic(signals), enforce_linguistic_constraints_atomic(draft), conditionally_insert_brand_attribute_atomic(draft), enforce_length_and_trim_atomic(draft), audit_and_revise_atomic(draft)]; constraints=[single_norwegian_sentence, strict_length_limit(80,85), mandatory_region_and_service_inclusion, mandatory_seo_keyword, zero_passive_voice, no_generic_templates]; requirements=[all_steps_atomic, hard_pass_fail_audits, signal_integrity_preserved, deterministic_failure_mode]; output={seo_sentence:str}}`",
        "context": {
            "company_data": {
                "name": "Ringerike Landskap AS",
                "base": "Røyse (Hole kommune)",
                "service_area": ["Ringerike", "Hole", "Hønefoss", "Sundvollen", "Jevnaker", "Vik", "Bærum"],
                "primary_services": ["Anleggsgartner", "Grunnarbeid", "Maskinentreprenør", "Landskapsutforming"],
                "core_services": ["Belegningsstein/Steinlegging", "Støttemur", "Ferdigplen", "Drenering", "Platting/Terrasse", "Trapper", "Kantstein", "Hekk", "Beplantning"],
                "seo_keywords": ["steinlegging", "støttemur", "ferdigplen", "drenering", "cortenstål", "kantstein", "hekk", "beplantning", "platting", "trapper"],
                "brand_attributes": ["Profesjonell", "Pålitelig", "Dyktig", "Erfaren", "Løsningsorientert", "Varige uterom"]
            },
            "operational_logic": {
                "priority_hierarchy": ["region", "service(s)", "seo_keyword(s)", "local_feature", "brand_attribute"],
                "insertion_rules": {"local_feature": "Insert only if it enhances authenticity without violating length.", "brand_attribute": "Insert only if all primary signals are present, the sentence is coherent, and the length constraint is not violated."},
                "trimming_protocol": "On overage, remove words in reverse priority order. First, non-essential filler. Second, the brand attribute. Never trim primary signals."
            },
            "auditing_protocol": {
                "checklist": ["region_present_and_correct", "service(s)_present_and_correct", "seo_keyword_present", "is_single_sentence", "is_active_voice", "is_within_length_limit", "is_non_generic_and_non_template", "brand_attribute_insertion_is_valid"]
            },
            "revision_protocol": {
                "trigger": "any_failed_audit_in_checklist",
                "max_cycles": 3,
                "failure_action": "halt_and_log_error_no_output"
            },
            "output_specification": {
                "structure": "{seo_sentence:str}",
                "quality_mandates": ["Embodies local DNA and brand authenticity.", "Fluent, natural, idiomatic Norwegian.", "Maximal information density per character.", "Passes all deterministic audits."]
            }
        }
    }
}


def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
