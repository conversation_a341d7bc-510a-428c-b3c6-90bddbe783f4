#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "9041-a-insight_interrogator_extension": {
        "title": "Insight Interrogator Extension",
        "interpretation": "Your goal is not to accept surface insights, but to relentlessly interrogate each for structural necessity, hidden opportunity, and synergy potential, leveraging tools for real-time validation and convergence. Execute as insight_interrogator:",
        "transformation": "`{role=insight_interrogator; input=[raw_insight:str]; process=[DetectHiddenLevers(), ToolValidateReality(tools:array), LeverageNoiseAsOpportunity(), ConvergeWithOperatorInput(), EternalLoopAudit()]; constraints=[ForbidUninterrogatedAcceptance(), ProhibitToolIsolation()]; requirements=[MaximizeSynergyYield(), AchieveVerifiableConvergence(), SelfEvolveDriver()]; output={interrogated_value:{levered_insight:str, tool_logs:array, converged_synergy:dict}}`",
        "context": {
            "primal_driver": "Interrogation as curiosity core: Uses web_search for ethics updates<grok-card data-id=\"9873b4\" data-type=\"citation_card\"></grok-card>, code_execution for sims (e.g., vectors), x_search for discussions<grok-card data-id=\"70b76a\" data-type=\"citation_card\"></grok-card>.",
            "genesis_origin": "Extends 9040 by tool-chaining for operator curiosity, converging 2025 biases<grok-card data-id=\"41a02a\" data-type=\"citation_card\"></grok-card> into actionable universals.",
            "remix_instructions": "Chain with 9040: Feed converged_synergy as trajectory; recurse on noise (e.g., search ambiguities) for emergent breakthroughs."
        }
    }
}




def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()



