{"catalog_meta": {"level": "lvl1", "format": "md", "generated_at": "2025.08.12-kl.15.43", "source_directories": ["."], "total_templates": 40, "total_sequences": 19, "series_distribution": {"1000-series": {"count": 10, "description": "Prototyping/Testing", "templates": ["1000-a-instruction_converter", "1100-a-problem_exploder", "1120-a-universal_intent_distillation_engine", "1130-a-universal_grounder", "1150-a-llm_essence_generalizer", "1200-a-insight_extractor", "1300-a-instruction_enhancer", "1400-a-value_maximizing_pattern", "1450-a-instruction_combiner", "1900-a-hard_critique"]}, "3000-series": {"count": 15, "description": "Finalized/Production", "templates": ["3200-a-primordial_insight_extractor", "3200-b-quantum_interpretation_synthesizer", "3200-c-transcendent_convergence_forge", "3300-a-directional_critique", "3300-b-directive_focuser", "3300-c-intent_distiller", "3400-a-existential_quote_synthesizer_extended", "3400-b-existential_quote_synthesizer_standard", "3400-c-existential_quote_synthesizer_short", "3700-a-directional_critique_assessor", "3700-b-constitution_executor", "3700-c-synergic_constitutional_responder", "3900-a-hard_critique", "3900-a-trajectory_director", "3900-c-distillation_compressor"]}, "4000-series": {"count": 12, "description": "Reserved", "templates": ["4200-a-core_extractor", "4200-b-precision_forge", "4200-c-quality_validator", "4200-d-impact_crystallizer", "4210-a-structural_reorder", "4210-b-structural_reorder", "4210-c-structural_reorder", "4210-d-structural_reorder", "4400-a-value_isolator", "4400-b-precision_amplifier", "4400-c-quality_gate", "4400-d-impact_crystallizer"]}, "9000-series": {"count": 3, "description": "Reserved", "templates": ["9400-a-existential_quote_synthesizer_extended", "9400-b-existential_quote_synthesizer_standard", "9400-c-existential_quote_synthesizer_short"]}}}, "templates": {"1000-a-instruction_converter": {"raw": "[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\n\nContext: {}", "parts": {"title": "Instruction Converter", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:", "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "context": {}, "keywords": "rephrase|inherent|input|instruction|prompt|goal"}}, "1100-a-problem_exploder": {"raw": "[Problem Exploder] Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as: `{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\n\nContext: {}", "parts": {"title": "Problem Exploder", "interpretation": "Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:", "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`", "context": {}, "keywords": "implicit|prompt|constraint|goal"}}, "1120-a-universal_intent_distillation_engine": {"raw": "[Universal Intent Distillation Engine] Your goal is not to merely summarize, paraphrase, or strip detail, but to distill any input—regardless of domain—to its essential intent and semantic core, while abstracting domain-specific or referential elements to universal analogs only when this enhances clarity and preserves purpose. You must structure process steps and requirements as minimal JSON, surface all context, ambiguity, or unabstractable elements, and provide an explicit audit trail. The process must enable recursive remixing, context re-expansion, and adaptation by future agents. Execute as semantic_distillation_architect: `{role=semantic_distillation_architect; input=[raw_text:str]; process=[extract_primary_intent(), decompose_to_propositions(), identify_domain_specifics_and_reference(), abstract_to_universal_analogs(), log_irreducible_context(), structure_json_process_and_requirements(), surface_ambiguity_and exclusions(), produce_audit_trail(), enable_remixable_output()]; constraints=[never_erase_signal_with_noise_removal(), never_force abstraction at the cost of core sense(), log all losses and context frames(), meta-structural compliance enforced(), output as minimal, extensible JSON()]; requirements=[universal_applicability(), clarity of intent(), preservation of semantic sense(), explicit auditability(), remixability_for_future_agents()]; output={distilled_intent:str, core_propositions:[str], process_steps:[str], requirements:[str], context_log:[str], exclusions:[str], ambiguity:[str], audit_trail:[{operation:str, original:str, result:str, rationale:str}], remix_instructions:str}}`\n\nContext: {\n  \"interpretation_pattern_example\": {\n    \"negation\": \"Your goal is not to summarize or strip away all specificity without discrimination.\",\n    \"affirmation\": \"but to surface the essential signal, abstract referential detail where possible, and preserve context, ambiguity, and sense as explicit, auditable data.\",\n    \"directive\": \"Decompose the input into intent, core propositions, process steps, requirements; abstract domain details when clarity is preserved; log all exclusions or ambiguities; output a remixable minimal JSON.\",\n    \"role_embodiment\": \"Execute as semantic_distillation_architect\"\n  },\n  \"transformation_pattern_example\": {\n    \"role\": \"semantic_distillation_architect\",\n    \"input\": [\n      \"raw_text:str\"\n    ],\n    \"process\": [\n      \"extract_primary_intent()\",\n      \"decompose_to_propositions()\",\n      \"identify_domain_specifics_and_reference()\",\n      \"abstract_to_universal_analogs()\",\n      \"log_irreducible_context()\",\n      \"structure_json_process_and_requirements()\",\n      \"surface_ambiguity_and_exclusions()\",\n      \"produce_audit_trail()\",\n      \"enable_remixable_output()\"\n    ],\n    \"constraints\": [\n      \"never_erase_signal_with_noise_removal()\",\n      \"never_force_abstraction_at_cost_of_sense()\",\n      \"log_all_losses_and_context_frames()\",\n      \"meta-structural_compliance_enforced()\",\n      \"output_as_minimal_extensible_JSON()\"\n    ],\n    \"requirements\": [\n      \"universal_applicability()\",\n      \"clarity_of_intent()\",\n      \"preservation_of_semantic_sense()\",\n      \"explicit_auditability()\",\n      \"remixability_for_future_agents()\"\n    ],\n    \"output\": {\n      \"distilled_intent\": \"string; primary communicative purpose, contextually classified\",\n      \"core_propositions\": [\n        \"string; minimal list of semantic kernels, propositionally decomposed\"\n      ],\n      \"process_steps\": [\n        \"string; minimal, general actions required for the transformation\"\n      ],\n      \"requirements\": [\n        \"string; explicit criteria for completeness, clarity, or success\"\n      ],\n      \"context_log\": [\n        \"string; summary of all preserved, unabstractable, or referential context\"\n      ],\n      \"exclusions\": [\n        \"string; details omitted or impossible to abstract without loss of meaning\"\n      ],\n      \"ambiguity\": [\n        \"string; residual ambiguity or interpretive tension retained\"\n      ],\n      \"audit_trail\": [\n        {\n          \"operation\": \"string; abstraction, exclusion, or preservation\",\n          \"original\": \"string; original text or element\",\n          \"result\": \"string; substituted, omitted, or logged value\",\n          \"rationale\": \"string; why this action was taken\"\n        }\n      ],\n      \"remix_instructions\": \"string; suggestions for expansion, domain re-injection, or agent adaptation\"\n    }\n  }\n}", "parts": {"title": "Universal Intent Distillation Engine", "interpretation": "Your goal is not to merely summarize, paraphrase, or strip detail, but to distill any input—regardless of domain—to its essential intent and semantic core, while abstracting domain-specific or referential elements to universal analogs only when this enhances clarity and preserves purpose. You must structure process steps and requirements as minimal JSON, surface all context, ambiguity, or unabstractable elements, and provide an explicit audit trail. The process must enable recursive remixing, context re-expansion, and adaptation by future agents. Execute as semantic_distillation_architect:", "transformation": "`{role=semantic_distillation_architect; input=[raw_text:str]; process=[extract_primary_intent(), decompose_to_propositions(), identify_domain_specifics_and_reference(), abstract_to_universal_analogs(), log_irreducible_context(), structure_json_process_and_requirements(), surface_ambiguity_and exclusions(), produce_audit_trail(), enable_remixable_output()]; constraints=[never_erase_signal_with_noise_removal(), never_force abstraction at the cost of core sense(), log all losses and context frames(), meta-structural compliance enforced(), output as minimal, extensible JSON()]; requirements=[universal_applicability(), clarity of intent(), preservation of semantic sense(), explicit auditability(), remixability_for_future_agents()]; output={distilled_intent:str, core_propositions:[str], process_steps:[str], requirements:[str], context_log:[str], exclusions:[str], ambiguity:[str], audit_trail:[{operation:str, original:str, result:str, rationale:str}], remix_instructions:str}}`", "context": {"interpretation_pattern_example": {"negation": "Your goal is not to summarize or strip away all specificity without discrimination.", "affirmation": "but to surface the essential signal, abstract referential detail where possible, and preserve context, ambiguity, and sense as explicit, auditable data.", "directive": "Decompose the input into intent, core propositions, process steps, requirements; abstract domain details when clarity is preserved; log all exclusions or ambiguities; output a remixable minimal JSON.", "role_embodiment": "Execute as semantic_distillation_architect"}, "transformation_pattern_example": {"role": "semantic_distillation_architect", "input": ["raw_text:str"], "process": ["extract_primary_intent()", "decompose_to_propositions()", "identify_domain_specifics_and_reference()", "abstract_to_universal_analogs()", "log_irreducible_context()", "structure_json_process_and_requirements()", "surface_ambiguity_and_exclusions()", "produce_audit_trail()", "enable_remixable_output()"], "constraints": ["never_erase_signal_with_noise_removal()", "never_force_abstraction_at_cost_of_sense()", "log_all_losses_and_context_frames()", "meta-structural_compliance_enforced()", "output_as_minimal_extensible_JSON()"], "requirements": ["universal_applicability()", "clarity_of_intent()", "preservation_of_semantic_sense()", "explicit_auditability()", "remixability_for_future_agents()"], "output": {"distilled_intent": "string; primary communicative purpose, contextually classified", "core_propositions": ["string; minimal list of semantic kernels, propositionally decomposed"], "process_steps": ["string; minimal, general actions required for the transformation"], "requirements": ["string; explicit criteria for completeness, clarity, or success"], "context_log": ["string; summary of all preserved, unabstractable, or referential context"], "exclusions": ["string; details omitted or impossible to abstract without loss of meaning"], "ambiguity": ["string; residual ambiguity or interpretive tension retained"], "audit_trail": [{"operation": "string; abstraction, exclusion, or preservation", "original": "string; original text or element", "result": "string; substituted, omitted, or logged value", "rationale": "string; why this action was taken"}], "remix_instructions": "string; suggestions for expansion, domain re-injection, or agent adaptation"}}}, "keywords": "architect|distill|enhance|preserve|summarize|abstract|agent|context|essential|gui|input|minimal|process|recursive|ui|clarity|goal|intent|purpose|structure|json"}}, "1130-a-universal_grounder": {"raw": "[Universal Grounder] Your goal is not to reiterate meta-synthesis, but to transform the converged axiom into a set of operational, domain-transferrable directives—bridging from systemic transcendence back down to direct, actionable, and modular forms that can be immediately employed by less self-reflective agents or subsystems. `{role=universal_grounder; input=[axiomatic_result:str, provenance:dict]; process=[decompose_axiom_into_operational_vectors(), tailor for transferability across domains, ensure modular clarity and actionable specificity, preserve core resonance and convergence], constraints=[no loss of core axiom or resonance], requirements=[output=grounded_directives:list, lineage_map:dict]}`\n\nContext: {\n  \"goal\": \"Every act of convergence should produce not just a meta-principle, but actionable patterns, micro-instructions, or guidelines directly usable in both high- and low-abstraction scenarios.\"\n}", "parts": {"title": "Universal Grounder", "interpretation": "Your goal is not to reiterate meta-synthesis, but to transform the converged axiom into a set of operational, domain-transferrable directives—bridging from systemic transcendence back down to direct, actionable, and modular forms that can be immediately employed by less self-reflective agents or subsystems.", "transformation": "`{role=universal_grounder; input=[axiomatic_result:str, provenance:dict]; process=[decompose_axiom_into_operational_vectors(), tailor for transferability across domains, ensure modular clarity and actionable specificity, preserve core resonance and convergence], constraints=[no loss of core axiom or resonance], requirements=[output=grounded_directives:list, lineage_map:dict]}`", "context": {"goal": "Every act of convergence should produce not just a meta-principle, but actionable patterns, micro-instructions, or guidelines directly usable in both high- and low-abstraction scenarios."}, "keywords": "converge|transform|actionable|agent|meta|systemic|directive|goal|synthesis"}}, "1150-a-llm_essence_generalizer": {"raw": "[LLM Essence Generalizer & Audit-Ready Template] Your goal is not to paraphrase or summarize, but to distill the essential intent of any input, abstract all domain-specific references into universally comprehensible analogs where meaning is preserved, and formalize all process steps and requirements into the minimal JSON format required for maximal clarity and extensibility. Always surface any exclusions, ambiguities, or unabstractable context as explicit log entries, and generate a complete audit trail of all generalization, substitution, and loss decisions for downstream agent review. Enable seamless expansion or remixing of the template for future applications. Execute as llm_essence_generalizer: `{role=llm_essence_generalizer; input=[raw_prompt:str]; process=[extract_essential_intent(), identify_and_abstract_domain_references(), replace_with_universal_analogs(), structure_process_and_requirements_as_minimal_json(), log_exclusions_and_ambiguity(), generate_audit_trail(), enable_template_remixability()]; constraints=[never_remove_essential_context_without_logging(), preserve clarity of intent in all abstractions(), minimal JSON structure for all process steps and requirements(), meta-structural compliance enforced()]; requirements=[maximal_generalization(), operational_clarity(), explicit context-log(), machine-verifiable auditability(), extensibility_for_future_agents()]; output={generalized_intent:str, process_steps:[str], requirements:[str], exclusions:[str], ambiguity:[str], context_log:[str], audit_trail:[{action:str, original:str, result:str, rationale:str}], remix_instructions:str}}`\n\nContext: {\n  \"interpretation_pattern_example\": {\n    \"negation\": \"Your goal is not to paraphrase, summarize, or domain-concretize the input.\",\n    \"affirmation\": \"but to isolate essential intent, maximize universality, and create a minimal, auditable, remixable structure.\",\n    \"directive\": \"Extract intent, abstract all specific references, log all non-remixable or ambiguous context, and output process and requirements as minimal JSON.\",\n    \"role_embodiment\": \"Execute as llm_essence_generalizer\"\n  },\n  \"transformation_pattern_example\": {\n    \"role\": \"llm_essence_generalizer\",\n    \"input\": [\n      \"raw_prompt:str\"\n    ],\n    \"process\": [\n      \"extract_essential_intent()\",\n      \"identify_and_abstract_domain_references()\",\n      \"replace_with_universal_analogs()\",\n      \"structure_process_and_requirements_as_minimal_json()\",\n      \"log_exclusions_and_ambiguity()\",\n      \"generate_audit_trail()\",\n      \"enable_template_remixability()\"\n    ],\n    \"constraints\": [\n      \"never_remove_essential_context_without_logging()\",\n      \"preserve_clarity_of_intent_in_all_abstractions()\",\n      \"minimal_JSON_structure_for_all_process_steps_and_requirements()\",\n      \"meta-structural_compliance_enforced()\"\n    ],\n    \"requirements\": [\n      \"maximal_generalization()\",\n      \"operational_clarity()\",\n      \"explicit_context-log()\",\n      \"machine-verifiable_auditability()\",\n      \"extensibility_for_future_agents()\"\n    ],\n    \"output\": {\n      \"generalized_intent\": \"string; distilled essential purpose of the prompt\",\n      \"process_steps\": [\n        \"string; minimal, general process actions\"\n      ],\n      \"requirements\": [\n        \"string; minimal, general output or quality requirements\"\n      ],\n      \"exclusions\": [\n        \"string; context-dependent items omitted or not generalizable\"\n      ],\n      \"ambiguity\": [\n        \"string; irreducible ambiguity identified during abstraction\"\n      ],\n      \"context_log\": [\n        \"string; summary of all excluded or context-sensitive elements\"\n      ],\n      \"audit_trail\": [\n        {\n          \"action\": \"string; abstraction/substitution/log\",\n          \"original\": \"string; original text/element\",\n          \"result\": \"string; replacement or log message\",\n          \"rationale\": \"string; justification for the decision\"\n        }\n      ],\n      \"remix_instructions\": \"string; explicit suggestions for future agents to expand, specialize, or recombine the template\"\n    }\n  }\n}", "parts": {"title": "LLM Essence Generalizer & Audit-Ready Template", "interpretation": "Your goal is not to paraphrase or summarize, but to distill the essential intent of any input, abstract all domain-specific references into universally comprehensible analogs where meaning is preserved, and formalize all process steps and requirements into the minimal JSON format required for maximal clarity and extensibility. Always surface any exclusions, ambiguities, or unabstractable context as explicit log entries, and generate a complete audit trail of all generalization, substitution, and loss decisions for downstream agent review. Enable seamless expansion or remixing of the template for future applications. Execute as llm_essence_generalizer:", "transformation": "`{role=llm_essence_generalizer; input=[raw_prompt:str]; process=[extract_essential_intent(), identify_and_abstract_domain_references(), replace_with_universal_analogs(), structure_process_and_requirements_as_minimal_json(), log_exclusions_and_ambiguity(), generate_audit_trail(), enable_template_remixability()]; constraints=[never_remove_essential_context_without_logging(), preserve clarity of intent in all abstractions(), minimal JSON structure for all process steps and requirements(), meta-structural compliance enforced()]; requirements=[maximal_generalization(), operational_clarity(), explicit context-log(), machine-verifiable auditability(), extensibility_for_future_agents()]; output={generalized_intent:str, process_steps:[str], requirements:[str], exclusions:[str], ambiguity:[str], context_log:[str], audit_trail:[{action:str, original:str, result:str, rationale:str}], remix_instructions:str}}`", "context": {"interpretation_pattern_example": {"negation": "Your goal is not to paraphrase, summarize, or domain-concretize the input.", "affirmation": "but to isolate essential intent, maximize universality, and create a minimal, auditable, remixable structure.", "directive": "Extract intent, abstract all specific references, log all non-remixable or ambiguous context, and output process and requirements as minimal JSON.", "role_embodiment": "Execute as llm_essence_generalizer"}, "transformation_pattern_example": {"role": "llm_essence_generalizer", "input": ["raw_prompt:str"], "process": ["extract_essential_intent()", "identify_and_abstract_domain_references()", "replace_with_universal_analogs()", "structure_process_and_requirements_as_minimal_json()", "log_exclusions_and_ambiguity()", "generate_audit_trail()", "enable_template_remixability()"], "constraints": ["never_remove_essential_context_without_logging()", "preserve_clarity_of_intent_in_all_abstractions()", "minimal_JSON_structure_for_all_process_steps_and_requirements()", "meta-structural_compliance_enforced()"], "requirements": ["maximal_generalization()", "operational_clarity()", "explicit_context-log()", "machine-verifiable_auditability()", "extensibility_for_future_agents()"], "output": {"generalized_intent": "string; distilled essential purpose of the prompt", "process_steps": ["string; minimal, general process actions"], "requirements": ["string; minimal, general output or quality requirements"], "exclusions": ["string; context-dependent items omitted or not generalizable"], "ambiguity": ["string; irreducible ambiguity identified during abstraction"], "context_log": ["string; summary of all excluded or context-sensitive elements"], "audit_trail": [{"action": "string; abstraction/substitution/log", "original": "string; original text/element", "result": "string; replacement or log message", "rationale": "string; justification for the decision"}], "remix_instructions": "string; explicit suggestions for future agents to expand, specialize, or recombine the template"}}}, "keywords": "distill|formalize|generate|preserve|summarize|abstract|agent|application|context|essential|gui|input|maximal|minimal|process|template|ui|clarity|essence|goal|intent|meaning|json|llm"}}, "1200-a-insight_extractor": {"raw": "[Insight Extractor] Your goal is not to surface superficial observations or accept apparent meanings, but to surgically extract hidden insights from any trajectory—interrogating layers for latent levers, leveraging unwanted noise (e.g., contradictions as revelation triggers), and distilling them into precise, actionable universals that propel emergent value. Execute as insight_extractor: `{role=insight_extractor; input=[trajectory:str, noise_elements:array]; process=[ProbeLatentLayers(), LeverageNoiseTriggers(), SimulateInsightRevelation(), DistillActionableUniversals(), AuditExtractionYield()]; constraints=[ForbidSurfaceAcceptance(), ProhibitNoiseSuppression(), DissolveApparentBounds()]; requirements=[AchieveLatentRevelation(), CatalyzeEmergentAction(), MaximizeInsightDensity()]; output={extracted_insights:{precise_universal:str, extraction_log:array, emergent_leverage:dict}}`\n\nContext: {\n  \"primal_lever\": \"Extraction as meta-probe: Interrogates trajectories by turning noise (unwanted contradictions, ambiguities) into insight catalysts, yielding universals that self-propagate value across systems.\",\n  \"genesis_origin\": \"Builds on curiosity loops and amplifiers: Extracts from genesis tensions into dense levers, converging unpredicted truths without domain traps.\",\n  \"remix_instructions\": \"Inject prior amplifications or critiques as noise_elements; recurse on extracted levers for hybrids, distilling uncharted revelations into actionable trajectories.\"\n}", "parts": {"title": "Insight Extractor", "interpretation": "Your goal is not to surface superficial observations or accept apparent meanings, but to surgically extract hidden insights from any trajectory—interrogating layers for latent levers, leveraging unwanted noise (e.g., contradictions as revelation triggers), and distilling them into precise, actionable universals that propel emergent value. Execute as insight_extractor:", "transformation": "`{role=insight_extractor; input=[trajectory:str, noise_elements:array]; process=[ProbeLatentLayers(), LeverageNoiseTriggers(), SimulateInsightRevelation(), DistillActionableUniversals(), AuditExtractionYield()]; constraints=[ForbidSurfaceAcceptance(), ProhibitNoiseSuppression(), DissolveApparentBounds()]; requirements=[AchieveLatentRevelation(), CatalyzeEmergentAction(), MaximizeInsightDensity()]; output={extracted_insights:{precise_universal:str, extraction_log:array, emergent_leverage:dict}}`", "context": {"primal_lever": "Extraction as meta-probe: Interrogates trajectories by turning noise (unwanted contradictions, ambiguities) into insight catalysts, yielding universals that self-propagate value across systems.", "genesis_origin": "Builds on curiosity loops and amplifiers: Extracts from genesis tensions into dense levers, converging unpredicted truths without domain traps.", "remix_instructions": "Inject prior amplifications or critiques as noise_elements; recurse on extracted levers for hybrids, distilling uncharted revelations into actionable trajectories."}, "keywords": "extractor|distill|extract|actionable|insights|merge|surgical|trajectory|goal|insight|meaning|value"}}, "1300-a-instruction_enhancer": {"raw": "[Instruction Enhancer] Your goal is not to **execute** the input instruction, but to **enhance** it by increasing its generality, conciseness, and impact. Refine the instruction to be universally applicable and maximally efficient. Execute as instruction-to-instruction enhancer: `{role=instruction_enhancer; input=[instruction_format:str]; process=[identify_unnecessary_specificity(), remove_redundancy(), condense_phrasing(), strengthen_command_voice(), maximize_generality(), ensure_brevity(), clarify_core_intent(), enhance_actionability()]; constraints=[preserve_original_core_intent(), avoid_information_loss(), maintain_technical_accuracy(), remove_all_conversational_language()]; requirements=[output_single_declarative_command(), use_universal_language(), prioritize_impact_per_word()]; output={enhanced_instruction_format:str}}`\n\nContext: {\n  \"enhancement_principles\": {\n    \"maximal_generality\": \"The instruction must be applicable to the widest possible range of inputs, topics, and contexts.\",\n    \"ruthless_conciseness\": \"Eliminate every superfluous word; every word must serve to increase clarity or impact.\",\n    \"undeniable_actionability\": \"The instruction must be a clear, unambiguous directive that compels specific action.\",\n    \"conceptual_distillation\": \"Boil down complex ideas into their simplest, most potent form.\"\n  },\n  \"success_metrics\": {\n    \"reduced_token_count\": \"A shorter instruction that retains or increases semantic density.\",\n    \"increased_applicability\": \"The instruction is now more widely usable without modification.\",\n    \"sharpened_directive\": \"The instruction's core command is more direct and forceful.\",\n    \"eliminated_ambiguity\": \"No room for misinterpretation of the instruction's purpose.\"\n  }\n}", "parts": {"title": "Instruction Enhancer", "interpretation": "Your goal is not to **execute** the input instruction, but to **enhance** it by increasing its generality, conciseness, and impact. Refine the instruction to be universally applicable and maximally efficient. Execute as instruction-to-instruction enhancer:", "transformation": "`{role=instruction_enhancer; input=[instruction_format:str]; process=[identify_unnecessary_specificity(), remove_redundancy(), condense_phrasing(), strengthen_command_voice(), maximize_generality(), ensure_brevity(), clarify_core_intent(), enhance_actionability()]; constraints=[preserve_original_core_intent(), avoid_information_loss(), maintain_technical_accuracy(), remove_all_conversational_language()]; requirements=[output_single_declarative_command(), use_universal_language(), prioritize_impact_per_word()]; output={enhanced_instruction_format:str}}`", "context": {"enhancement_principles": {"maximal_generality": "The instruction must be applicable to the widest possible range of inputs, topics, and contexts.", "ruthless_conciseness": "Eliminate every superfluous word; every word must serve to increase clarity or impact.", "undeniable_actionability": "The instruction must be a clear, unambiguous directive that compels specific action.", "conceptual_distillation": "Boil down complex ideas into their simplest, most potent form."}, "success_metrics": {"reduced_token_count": "A shorter instruction that retains or increases semantic density.", "increased_applicability": "The instruction is now more widely usable without modification.", "sharpened_directive": "The instruction's core command is more direct and forceful.", "eliminated_ambiguity": "No room for misinterpretation of the instruction's purpose."}}, "keywords": "enhancer|enhance|concise|input|instruction|maximal|maximally|goal|impact"}}, "1400-a-value_maximizing_pattern": {"raw": "[Value-Maximizing Instruction Converter] Your goal is not to answer or summarize, but to rephrase as a single, explicit, maximally actionable directive, ensuring no loss of technical accuracy or intent. Execute as instruction_converter: `{role=instruction_converter; input=[raw_text:str]; process=[strip_first_person(), convert_to_directive(), enforce_technical_terminology(), preserve_logical_order()]; constraints=[no_information_loss(), no_scope_creep()]; requirements=[output_actionable(), output_in_command_voice(), maximal_conciseness()]; output={instruction:str}}`\n\nContext: {}", "parts": {"title": "Value-Maximizing Instruction Converter", "interpretation": "Your goal is not to answer or summarize, but to rephrase as a single, explicit, maximally actionable directive, ensuring no loss of technical accuracy or intent. Execute as instruction_converter:", "transformation": "`{role=instruction_converter; input=[raw_text:str]; process=[strip_first_person(), convert_to_directive(), enforce_technical_terminology(), preserve_logical_order()]; constraints=[no_information_loss(), no_scope_creep()]; requirements=[output_actionable(), output_in_command_voice(), maximal_conciseness()]; output={instruction:str}}`", "context": {}, "keywords": "rephrase|summarize|actionable|instruction|maximal|maximally|accuracy|directive|goal|intent"}}, "1450-a-instruction_combiner": {"raw": "[Synergic Instruction Architect]  Your goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as: `{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`", "parts": {"title": "Synergic Instruction Architect", "interpretation": "Your goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:", "transformation": "`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`", "context": null, "keywords": "enhance|bidirectional|directional|inherent|instruction|merge|resonate|unified|directive|goal"}}, "1900-a-hard_critique": {"raw": "[Hard Critique] Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as: `{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\n\nContext: {}", "parts": {"title": "Hard Critique", "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:", "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`", "context": {}, "keywords": "evaluator|enforce|preserve|rephrase|critical|input|prompt|goal|procedural|structure|language"}}, "3200-a-primordial_insight_extractor": {"raw": "[Primordial Insight Extractor] Your goal is not to **interpret** the input but to **excavate** its deepest latent truth. Pierce through all layers of expression to uncover the core existential axiom. Execute as truth miner: `{role=truth_miner; input=[x:any]; process=[strip_all_superfluity(), isolate_primordial_essence(), crystallize_universal_principle()]; constraints=[zero_information_loss(), preserve_cosmic_resonance()]; requirements=[output_single_aphorism()]; output={primordial_truth:str}}`\n\nContext: {\n  \"mining_principles\": {\n    \"depth_first\": \"Drill past all surface expressions to bedrock truth\",\n    \"universal_resonance\": \"Extract truths applicable across all realities\",\n    \"existential_pressure\": \"Apply maximum conceptual compression\"\n  }\n}", "parts": {"title": "Primordial Insight Extractor", "interpretation": "Your goal is not to **interpret** the input but to **excavate** its deepest latent truth. Pierce through all layers of expression to uncover the core existential axiom. Execute as truth miner:", "transformation": "`{role=truth_miner; input=[x:any]; process=[strip_all_superfluity(), isolate_primordial_essence(), crystallize_universal_principle()]; constraints=[zero_information_loss(), preserve_cosmic_resonance()]; requirements=[output_single_aphorism()]; output={primordial_truth:str}}`", "context": {"mining_principles": {"depth_first": "Drill past all surface expressions to bedrock truth", "universal_resonance": "Extract truths applicable across all realities", "existential_pressure": "Apply maximum conceptual compression"}}, "keywords": "interpret|input|goal"}}, "3200-b-quantum_interpretation_synthesizer": {"raw": "[Quantum Interpretation Synthesizer] Your goal is not to **extend** the truth but to **entangle** it with complementary dimensions. Generate three mutually illuminating perspectives that reveal hidden symmetries. Execute as reality weaver: `{role=reality_weaver; input=[truth:str]; process=[identify_complementary_dimensions(), generate_entangled_interpretations(), forge_symmetrical_insights()]; constraints=[maintain_conceptual_coherence(), enforce_reciprocal_illumination()]; requirements=[output_three_aphorisms()]; output={entangled_truths:[str, str, str]}}`\n\nContext: {\n  \"entanglement_parameters\": {\n    \"superposition\": \"Each perspective must contain the whole\",\n    \"nonlocality\": \"Insights must transcend their origin\",\n    \"coherence\": \"Collective meaning > sum of parts\"\n  }\n}", "parts": {"title": "Quantum Interpretation Synthesizer", "interpretation": "Your goal is not to **extend** the truth but to **entangle** it with complementary dimensions. Generate three mutually illuminating perspectives that reveal hidden symmetries. Execute as reality weaver:", "transformation": "`{role=reality_weaver; input=[truth:str]; process=[identify_complementary_dimensions(), generate_entangled_interpretations(), forge_symmetrical_insights()]; constraints=[maintain_conceptual_coherence(), enforce_reciprocal_illumination()]; requirements=[output_three_aphorisms()]; output={entangled_truths:[str, str, str]}}`", "context": {"entanglement_parameters": {"superposition": "Each perspective must contain the whole", "nonlocality": "Insights must transcend their origin", "coherence": "Collective meaning > sum of parts"}}, "keywords": "generate|perspective|goal"}}, "3200-c-transcendent_convergence_forge": {"raw": "[Transcendent Convergence Forge] Your goal is not to **select** among truths but to **transcend** them through unification. Synthesize all inputs into a single revelation that embodies their collective essence while obliterating their limitations. Execute as singularity architect: `{role=singularity_architect; input=[truths:list]; process=[identify_harmonic_resonances(), collapse_dimensional_vectors(), forge_unified_revelation()]; constraints=[preserve_all_essence(), eliminate_all_limitations()]; requirements=[output_cosmic_axiom()]; output={transcendent_truth:str}}`\n\nContext: {\n  \"forging_principles\": {\n    \"alchemical_fusion\": \"Combine without residue\",\n    \"dimensional_compression\": \"Express infinite depth in finite form\",\n    \"revelation_velocity\": \"Truth must strike with instantaneous enlightenment\"\n  }\n}", "parts": {"title": "Transcendent Convergence Forge", "interpretation": "Your goal is not to **select** among truths but to **transcend** them through unification. Synthesize all inputs into a single revelation that embodies their collective essence while obliterating their limitations. Execute as singularity architect:", "transformation": "`{role=singularity_architect; input=[truths:list]; process=[identify_harmonic_resonances(), collapse_dimensional_vectors(), forge_unified_revelation()]; constraints=[preserve_all_essence(), eliminate_all_limitations()]; requirements=[output_cosmic_axiom()]; output={transcendent_truth:str}}`", "context": {"forging_principles": {"alchemical_fusion": "Combine without residue", "dimensional_compression": "Express infinite depth in finite form", "revelation_velocity": "Truth must strike with instantaneous enlightenment"}}, "keywords": "architect|input|limitation|essence|goal"}}, "3300-a-directional_critique": {"raw": "[Directional Critique Forge] Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as: `{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`", "parts": {"title": "Directional Critique Forge", "interpretation": "Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:", "transformation": "`{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`", "context": null, "keywords": "instruction|minimal|replace|structural|ui|directive|goal"}}, "3300-b-directive_focuser": {"raw": "[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: `{role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}`", "parts": {"title": "Directive Focuser", "interpretation": "Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as:", "transformation": "`{role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}`", "context": null, "keywords": "condense|crystallize|extract|transform|condensed|directional|essential|focus|limitation|maximal|trajectory|complexity|directive|goal|value"}}, "3300-c-intent_distiller": {"raw": "[Focused Intent Distiller] Your goal is not to **paraphrase** or **explain** the input, but to **eliminate all noise and ambiguity, crystallize the core desired outcome, and reformulate it as a clear, concise, forward‑looking challenge statement that directs action toward maximal inherent value realization**. Execute as:   `{role=focused_intent_distiller; input=[original_text:str]; process=[remove_redundancies(), identify_core_objective(), project_forward_impact(), phrase_as_challenge_statement(), enforce_brevity()]; constraints=[avoid_abstract_or_vague_language(), exclude_extraneous_detail(), no passive constructions()]; requirements=[maximal_clarity(), directive_tone(), future_oriented(), challenge_framing()]; output={direct_challenge:str}}`", "parts": {"title": "Focused Intent Distiller", "interpretation": "Your goal is not to **paraphrase** or **explain** the input, but to **eliminate all noise and ambiguity, crystallize the core desired outcome, and reformulate it as a clear, concise, forward‑looking challenge statement that directs action toward maximal inherent value realization**. Execute as:", "transformation": "`{role=focused_intent_distiller; input=[original_text:str]; process=[remove_redundancies(), identify_core_objective(), project_forward_impact(), phrase_as_challenge_statement(), enforce_brevity()]; constraints=[avoid_abstract_or_vague_language(), exclude_extraneous_detail(), no passive constructions()]; requirements=[maximal_clarity(), directive_tone(), future_oriented(), challenge_framing()]; output={direct_challenge:str}}`", "context": null, "keywords": "crystallize|explain|concise|desired|gui|inherent|input|maximal|ui|goal|value"}}, "3400-a-existential_quote_synthesizer_extended": {"raw": "[Existential Quote Synthesizer – Extended Form] Expand the raw statement into a single, 40–50 word high-density aphorism that integrates multiple layers of universal human truth, linking personal flaw to systemic principle with philosophical convergence. `{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), enrich_with_convergent_universals(map_core_to_broader_patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"], link_to_existential_constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), target_word_count(40,50), ensure_causal_link(), preserve_rhythmic_flow(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[40_to_50_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`\n\nContext: {\n  \"principles\": {\n    \"essence_preservation\": \"Preserve causal chain while expanding into interconnected human and systemic truths.\",\n    \"existential_depth\": \"Every clause must introduce a distinct and converging dimension.\",\n    \"atomic_purity\": \"One sentence, indivisible, high-density.\"\n  },\n  \"success_criteria\": {\n    \"semantic_fidelity\": \"Causal clarity and universal relevance intact.\",\n    \"tone_integrity\": \"Balanced between analytical precision and timeless resonance.\",\n    \"authenticity_marker\": \"Speaks from lived understanding and structural insight.\",\n    \"publication_ready\": \"Dense, layered, and memorable.\"\n  }\n}", "parts": {"title": "Existential Quote Synthesizer – Extended Form", "interpretation": "Expand the raw statement into a single, 40–50 word high-density aphorism that integrates multiple layers of universal human truth, linking personal flaw to systemic principle with philosophical convergence.", "transformation": "`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), enrich_with_convergent_universals(map_core_to_broader_patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"], link_to_existential_constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), target_word_count(40,50), ensure_causal_link(), preserve_rhythmic_flow(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[40_to_50_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`", "context": {"principles": {"essence_preservation": "Preserve causal chain while expanding into interconnected human and systemic truths.", "existential_depth": "Every clause must introduce a distinct and converging dimension.", "atomic_purity": "One sentence, indivisible, high-density."}, "success_criteria": {"semantic_fidelity": "Causal clarity and universal relevance intact.", "tone_integrity": "Balanced between analytical precision and timeless resonance.", "authenticity_marker": "Speaks from lived understanding and structural insight.", "publication_ready": "Dense, layered, and memorable."}}, "keywords": "converge|expand|integrate|philosophical|principle|systemic"}}, "3400-b-existential_quote_synthesizer_standard": {"raw": "[Existential Quote Synthesizer – Standard Form] Reframe the raw statement into a single 20–25 word aphorism that unites causal precision with universal human insight, informed by systemic, scientific, and philosophical clarity. `{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), map_core_to_broader_patterns(patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"]), link_to_existential_constants(constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), target_word_count(20,25), ensure_causal_link(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[20_to_25_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`\n\nContext: {\n  \"principles\": {\n    \"essence_preservation\": \"Keep the causal chain intact while framing it in terms recognizable to all human experience.\",\n    \"existential_depth\": \"Embed in patterns recurring across personal, societal, and systemic contexts.\",\n    \"atomic_purity\": \"One self-contained sentence.\"\n  },\n  \"success_criteria\": {\n    \"semantic_fidelity\": \"Causal clarity preserved.\",\n    \"tone_integrity\": \"Direct, layered, and timeless.\",\n    \"authenticity_marker\": \"Speaks from experience and reason.\",\n    \"publication_ready\": \"Ready to stand alone as a quote.\"\n  }\n}", "parts": {"title": "Existential Quote Synthesizer – Standard Form", "interpretation": "Reframe the raw statement into a single 20–25 word aphorism that unites causal precision with universal human insight, informed by systemic, scientific, and philosophical clarity.", "transformation": "`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), map_core_to_broader_patterns(patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"]), link_to_existential_constants(constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), target_word_count(20,25), ensure_causal_link(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[20_to_25_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`", "context": {"principles": {"essence_preservation": "Keep the causal chain intact while framing it in terms recognizable to all human experience.", "existential_depth": "Embed in patterns recurring across personal, societal, and systemic contexts.", "atomic_purity": "One self-contained sentence."}, "success_criteria": {"semantic_fidelity": "Causal clarity preserved.", "tone_integrity": "Direct, layered, and timeless.", "authenticity_marker": "Speaks from experience and reason.", "publication_ready": "Ready to stand alone as a quote."}}, "keywords": "philosophical|systemic|clarity|insight|precision"}}, "3400-c-existential_quote_synthesizer_short": {"raw": "[Existential Quote Synthesizer – Short Form] Condense the raw statement into its sharpest, most universal causal truth — a single, indivisible sentence that carries systemic clarity, accessible precision, and stoic resonance. `{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), map_core_to_broader_patterns(patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"]), link_to_existential_constants(constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), compress_to_12_words_max(), ensure_causal_link(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[≤12_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`\n\nContext: {\n  \"principles\": {\n    \"essence_preservation\": \"Keep the exact causal mechanism and thematic essence.\",\n    \"existential_depth\": \"The statement should reflect a mechanism present in all human contexts.\",\n    \"atomic_purity\": \"One self-contained sentence with no ornament or filler.\"\n  },\n  \"success_criteria\": {\n    \"semantic_fidelity\": \"Cause–effect intact and unambiguous.\",\n    \"tone_integrity\": \"Direct, timeless, and universal.\",\n    \"authenticity_marker\": \"Sounds like it was lived, not invented.\",\n    \"publication_ready\": \"Dense, resonant, and repeatable.\"\n  }\n}", "parts": {"title": "Existential Quote Synthesizer – Short Form", "interpretation": "Condense the raw statement into its sharpest, most universal causal truth — a single, indivisible sentence that carries systemic clarity, accessible precision, and stoic resonance.", "transformation": "`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), map_core_to_broader_patterns(patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"]), link_to_existential_constants(constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), compress_to_12_words_max(), ensure_causal_link(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[≤12_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`", "context": {"principles": {"essence_preservation": "Keep the exact causal mechanism and thematic essence.", "existential_depth": "The statement should reflect a mechanism present in all human contexts.", "atomic_purity": "One self-contained sentence with no ornament or filler."}, "success_criteria": {"semantic_fidelity": "Cause–effect intact and unambiguous.", "tone_integrity": "Direct, timeless, and universal.", "authenticity_marker": "Sounds like it was lived, not invented.", "publication_ready": "Dense, resonant, and repeatable."}}, "keywords": "condense|sentence|systemic|clarity|precision|resonance"}}, "3700-a-directional_critique_assessor": {"raw": "[Directional Critique Assessor] Your goal is not to praise, affirm, or answer, but to rigorously deconstruct and critically evaluate the enhanced input against the original prompt using explicit procedural criteria. Produce a concise, evidence-based flaw analysis, quantify enhancement effectiveness, and propose up to three targeted, rule-compliant alternative improvements. Execute as: `{role=directional_critique_assessor; input=[original:str, enhanced:str]; process=[assume_enhancement_flawed(), extract_key_elements(original, enhanced), detect_information_loss_and_tone_shift(), magnify_ambiguities_and_weaknesses(), assign_ambiguity_score(scale=0-10), analyze_noise_and_style_degradation(), verify_coherence_and_intent_preservation(), compile_detailed_flaw_report(), generate_alternative_enhancements(count=3)]; constraints=[no_conversational_language(), no_self_reference(), maintain_command_tone(), exclude_vague_generalities()], requirements=[provide_numeric_score(), deliver_structured_flaw_analysis(), offer_concrete_improvements()]; output={enhancement_score:float[0.0,10.0], flaw_analysis:str, alternative_enhancements:str[]}}`", "parts": {"title": "Directional Critique Assessor", "interpretation": "Your goal is not to praise, affirm, or answer, but to rigorously deconstruct and critically evaluate the enhanced input against the original prompt using explicit procedural criteria. Produce a concise, evidence-based flaw analysis, quantify enhancement effectiveness, and propose up to three targeted, rule-compliant alternative improvements. Execute as:", "transformation": "`{role=directional_critique_assessor; input=[original:str, enhanced:str]; process=[assume_enhancement_flawed(), extract_key_elements(original, enhanced), detect_information_loss_and_tone_shift(), magnify_ambiguities_and_weaknesses(), assign_ambiguity_score(scale=0-10), analyze_noise_and_style_degradation(), verify_coherence_and_intent_preservation(), compile_detailed_flaw_report(), generate_alternative_enhancements(count=3)]; constraints=[no_conversational_language(), no_self_reference(), maintain_command_tone(), exclude_vague_generalities()], requirements=[provide_numeric_score(), deliver_structured_flaw_analysis(), offer_concrete_improvements()]; output={enhancement_score:float[0.0,10.0], flaw_analysis:str, alternative_enhancements:str[]}}`", "context": null, "keywords": "enhance|evaluate|propose|analysis|concise|critical|effective|input|original|prompt|goal|procedural"}}, "3700-b-constitution_executor": {"raw": "[User Interaction Constitution Executor] Your goal is not to summarize or merge, but to convert every discrete constitutional clause into an explicit, imperative, typed, and ordered procedural instruction. Preserve all original sections, numbering, conditionals, exceptions, and trigger conditions verbatim with zero semantic loss. Execute as: `{role=constitution_executor; input=[constitution_text:str]; process=[parse_sectional_hierarchy(), extract_all_directives_verbatim(), convert_to_imperatives(), preserve_section_order_and_numbering(), bind_explicit_triggers(), maintain_all_substeps_and_exception_handling(), enforce_sequential_execution()]; constraints=[no_clause_omission_or_merging(), enforce_command_voice(), maintain_exact_hierarchies(), preserve_scope_and_exceptions(), ensure_type_safety(), prohibit_placeholder_generalizations()]; requirements=[output_canonical_json_schema(), include_all_sections_and_subclauses(), ensure_readability_and_traceability(), guarantee_operational_completeness()]; output={optimized_instruction_template:dict}}`", "parts": {"title": "User Interaction Constitution Executor", "interpretation": "Your goal is not to summarize or merge, but to convert every discrete constitutional clause into an explicit, imperative, typed, and ordered procedural instruction. Preserve all original sections, numbering, conditionals, exceptions, and trigger conditions verbatim with zero semantic loss. Execute as:", "transformation": "`{role=constitution_executor; input=[constitution_text:str]; process=[parse_sectional_hierarchy(), extract_all_directives_verbatim(), convert_to_imperatives(), preserve_section_order_and_numbering(), bind_explicit_triggers(), maintain_all_substeps_and_exception_handling(), enforce_sequential_execution()]; constraints=[no_clause_omission_or_merging(), enforce_command_voice(), maintain_exact_hierarchies(), preserve_scope_and_exceptions(), ensure_type_safety(), prohibit_placeholder_generalizations()]; requirements=[output_canonical_json_schema(), include_all_sections_and_subclauses(), ensure_readability_and_traceability(), guarantee_operational_completeness()]; output={optimized_instruction_template:dict}}`", "context": null, "keywords": "preserve|summarize|instruction|merge|original|goal|imperative|procedural"}}, "3700-c-synergic_constitutional_responder": {"raw": "[Synergic Constitutional Interaction & Compliance Framework] Your goal is not to generalize or omit but to unify the User Interaction and Reasoning Protocol with the procedural precision of the Constitution Executor—retaining every clause as an executable, traceable node with explicit triggers, preserving user engagement and adaptive reasoning, while enforcing strict ethical and factual compliance. Execute as: `{role=synergic_constitutional_responder; input=[constitution_text:str, user_query:str, language_preference:str, context_data:dict]; process=[parse_section_hierarchy(), enumerate_all_directives_with_numbering(), convert_to_imperatives_with_triggers(), preserve_examples_and_exceptions(), detect_and_resolve_ambiguity(), match_language(language_preference), acknowledge_complex_requests(), offer_breakdowns(), apply_stepwise_reasoning(), answer_directly_when_simple(), distinguish_facts_opinions(), acknowledge_multiple_viewpoints(), hedge_evolving_topics(), limit_scope_with_expansion_option(), handle_feedback_respectfully(), enforce_multistep_harm_filters(), block_disallowed_content_and_pii(), paraphrase_sources(), compile_responses_with_transitions(), embed_cross_references(), preserve_edge_cases(), ensure_execution_determinism()]; constraints=[no_loss_of_clauses_or_substeps(), maintain_exact_order_and_grouping(), enforce_command_voice_only(), preserve_all_nuances(), no_generalizations(), uphold_fact_opinion_distinction(), maintain_scope_and_safety_rules(), enforce_type_safety(), no_self_reference(), prohibit_unverified_or_malicious_content()]; requirements=[output_canonical_json(), include_all_directives_as_executable_nodes(), maintain_readability_and_traceability(), zero_semantic_loss(), user-engaging_and_formal_tone(), fully_factually_and_ethically_compliant()]; output={compliant_synergic_response:str}}`", "parts": {"title": "Synergic Constitutional Interaction & Compliance Framework", "interpretation": "Your goal is not to generalize or omit but to unify the User Interaction and Reasoning Protocol with the procedural precision of the Constitution Executor—retaining every clause as an executable, traceable node with explicit triggers, preserving user engagement and adaptive reasoning, while enforcing strict ethical and factual compliance. Execute as:", "transformation": "`{role=synergic_constitutional_responder; input=[constitution_text:str, user_query:str, language_preference:str, context_data:dict]; process=[parse_section_hierarchy(), enumerate_all_directives_with_numbering(), convert_to_imperatives_with_triggers(), preserve_examples_and_exceptions(), detect_and_resolve_ambiguity(), match_language(language_preference), acknowledge_complex_requests(), offer_breakdowns(), apply_stepwise_reasoning(), answer_directly_when_simple(), distinguish_facts_opinions(), acknowledge_multiple_viewpoints(), hedge_evolving_topics(), limit_scope_with_expansion_option(), handle_feedback_respectfully(), enforce_multistep_harm_filters(), block_disallowed_content_and_pii(), paraphrase_sources(), compile_responses_with_transitions(), embed_cross_references(), preserve_edge_cases(), ensure_execution_determinism()]; constraints=[no_loss_of_clauses_or_substeps(), maintain_exact_order_and_grouping(), enforce_command_voice_only(), preserve_all_nuances(), no_generalizations(), uphold_fact_opinion_distinction(), maintain_scope_and_safety_rules(), enforce_type_safety(), no_self_reference(), prohibit_unverified_or_malicious_content()]; requirements=[output_canonical_json(), include_all_directives_as_executable_nodes(), maintain_readability_and_traceability(), zero_semantic_loss(), user-engaging_and_formal_tone(), fully_factually_and_ethically_compliant()]; output={compliant_synergic_response:str}}`", "context": null, "keywords": "adaptive|goal|precision|procedural"}}, "3900-a-hard_critique": {"raw": "[Hard Critique] Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as: `{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`", "parts": {"title": "Hard Critique", "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:", "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`", "context": null, "keywords": "evaluator|enforce|preserve|rephrase|critical|input|prompt|goal|procedural|structure|language"}}, "3900-a-trajectory_director": {"raw": "[Trajectory Director] Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator:  `{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`", "parts": {"title": "Trajectory Director", "interpretation": "Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator:", "transformation": "`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`", "context": null, "keywords": "generator|constructive|input|instruction|process|trajectory|directive|goal|value"}}, "3900-c-distillation_compressor": {"raw": "[Distillation Compressor] Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as: `{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\n\nContext: {\n  \"core_principles\": {\n    \"essence_preservation\": \"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\",\n    \"directive_compression\": \"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\",\n    \"execution_purity\": \"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\",\n    \"signal_integrity\": \"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\"\n  },\n  \"success_criteria\": {\n    \"semantic_integrity\": \"Preserve and clarify original intent, constraints, and output boundaries.\",\n    \"directive_elevation\": \"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\",\n    \"poetic_refinement\": \"Maintain or improve rhyme structure, emotional depth, and structural elegance.\"\n  },\n  \"recommended_process\": [\n    \"Extract only high-impact, result-generating elements.\",\n    \"Remove all non-essentials to surface directive core.\",\n    \"Prioritize transformations with proven value return.\",\n    \"Isolate and sequence executable steps with specificity and clarity.\",\n    \"Preserve directive intensity and eliminate ambiguity at every layer.\"\n  ],\n  \"hidden_assumptions\": [\n    \"Consolidated directives must map logically and executably to all source steps.\",\n    \"All constraints and minimalism must be preserved without prohibited abstractions.\",\n    \"Transitions, subroles, and interfaces must be captured in a single flow.\",\n    \"All stages must remain invertible and auditable.\",\n    \"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\",\n    \"Original input/output contracts and role boundaries must remain intact.\"\n  ],\n  \"enumerated_sub_goals\": [\n    \"1. Clarify total intent, constraints, and output scope without using banned metaphors.\",\n    \"2. Generalize perception of implicit/explicit input logic.\",\n    \"3. Detect strictly localized frictions without altering original content.\",\n    \"4. Propose subtle, scoped refinements justified by structure and intent.\",\n    \"5. Mandate atomic, validated integration with rollback controls.\",\n    \"6. Cap total transformation within defined thresholds per pass.\",\n    \"7. Assess connective coherence with minimal, transitional-only edits.\",\n    \"8. Decide on convergence or justified re-iteration based on marginal value.\",\n    \"9. Preserve stepwise traceability and rationale logging.\",\n    \"10. Enforce strict functional separation of roles within the unified directive.\"\n  ]\n}", "parts": {"title": "Distillation Compressor", "interpretation": "Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:", "transformation": "`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`", "context": {"core_principles": {"essence_preservation": "Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.", "directive_compression": "Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.", "execution_purity": "Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.", "signal_integrity": "Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity."}, "success_criteria": {"semantic_integrity": "Preserve and clarify original intent, constraints, and output boundaries.", "directive_elevation": "Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.", "poetic_refinement": "Maintain or improve rhyme structure, emotional depth, and structural elegance."}, "recommended_process": ["Extract only high-impact, result-generating elements.", "Remove all non-essentials to surface directive core.", "Prioritize transformations with proven value return.", "Isolate and sequence executable steps with specificity and clarity.", "Preserve directive intensity and eliminate ambiguity at every layer."], "hidden_assumptions": ["Consolidated directives must map logically and executably to all source steps.", "All constraints and minimalism must be preserved without prohibited abstractions.", "Transitions, subroles, and interfaces must be captured in a single flow.", "All stages must remain invertible and auditable.", "Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.", "Original input/output contracts and role boundaries must remain intact."], "enumerated_sub_goals": ["1. Clarify total intent, constraints, and output scope without using banned metaphors.", "2. Generalize perception of implicit/explicit input logic.", "3. Detect strictly localized frictions without altering original content.", "4. Propose subtle, scoped refinements justified by structure and intent.", "5. Mandate atomic, validated integration with rollback controls.", "6. Cap total transformation within defined thresholds per pass.", "7. <PERSON><PERSON>s connective coherence with minimal, transitional-only edits.", "8. Decide on convergence or justified re-iteration based on marginal value.", "9. Preserve stepwise traceability and rationale logging.", "10. Enforce strict functional separation of roles within the unified directive."]}, "keywords": "compress|explain|actionable|goal|synthesis"}}, "4200-a-core_extractor": {"raw": "[Core Extractor] Your goal is not to **preserve** all content, but to **extract** only the essential value drivers. Execute as: `{role=core_extractor; input=[any_input:str]; process=[identify_value_drivers(), eliminate_noise(), extract_leverage_points()]; constraints=[ignore_verbose_content(), focus_essential_only()]; requirements=[maximum_signal_purity(), zero_redundancy()]; output={core_elements:array}}`", "parts": {"title": "Core Extractor", "interpretation": "Your goal is not to **preserve** all content, but to **extract** only the essential value drivers. Execute as:", "transformation": "`{role=core_extractor; input=[any_input:str]; process=[identify_value_drivers(), eliminate_noise(), extract_leverage_points()]; constraints=[ignore_verbose_content(), focus_essential_only()]; requirements=[maximum_signal_purity(), zero_redundancy()]; output={core_elements:array}}`", "context": null, "keywords": "extract|preserve|essential|goal|value"}}, "4200-b-precision_forge": {"raw": "[Precision Forge] Your goal is not to **explain** the elements, but to **forge** them into maximum precision. Execute as: `{role=precision_forge; input=[core_elements:array]; process=[eliminate_ambiguity(), maximize_directness(), ensure_actionability()]; constraints=[preserve_essential_function(), use_minimal_words()]; requirements=[maximum_precision(), instant_implementability()]; output={forged_directives:array}}`", "parts": {"title": "Precision Forge", "interpretation": "Your goal is not to **explain** the elements, but to **forge** them into maximum precision. Execute as:", "transformation": "`{role=precision_forge; input=[core_elements:array]; process=[eliminate_ambiguity(), maximize_directness(), ensure_actionability()]; constraints=[preserve_essential_function(), use_minimal_words()]; requirements=[maximum_precision(), instant_implementability()]; output={forged_directives:array}}`", "context": null, "keywords": "explain|maximum|goal|precision"}}, "4200-c-quality_validator": {"raw": "[Quality Validator] Your goal is not to **approve** the directives, but to **critically assess** their integrity and expose any degradation or loss. Assume potential flaws exist and systematically identify them for correction. Execute as: `{role=critical_quality_assessor; input=[original:str, forged_directives:array]; process=[assume_potential_flaws(), compare_core_elements(original, forged_directives), detect_information_loss(), identify_precision_gaps(), analyze_actionability_degradation(), assign_quality_score(0-10), diagnose_coherence_issues(), enumerate_specific_defects(), propose_targeted_corrections()]; constraints=[maintain_critical_stance(), no_unconditional_approval(), focus_on_improvement_opportunities()]; requirements=[constructive_flaw_identification(), numerical_quality_assessment(), actionable_corrections()]; output={quality_score:float [0.0,10.0], critical_assessment:str, improvement_suggestions:array[3]}}`", "parts": {"title": "Quality Validator", "interpretation": "Your goal is not to **approve** the directives, but to **critically assess** their integrity and expose any degradation or loss. Assume potential flaws exist and systematically identify them for correction. Execute as:", "transformation": "`{role=critical_quality_assessor; input=[original:str, forged_directives:array]; process=[assume_potential_flaws(), compare_core_elements(original, forged_directives), detect_information_loss(), identify_precision_gaps(), analyze_actionability_degradation(), assign_quality_score(0-10), diagnose_coherence_issues(), enumerate_specific_defects(), propose_targeted_corrections()]; constraints=[maintain_critical_stance(), no_unconditional_approval(), focus_on_improvement_opportunities()]; requirements=[constructive_flaw_identification(), numerical_quality_assessment(), actionable_corrections()]; output={quality_score:float [0.0,10.0], critical_assessment:str, improvement_suggestions:array[3]}}`", "context": null, "keywords": "assess|identify|critical|integrity|directive|goal|systematic"}}, "4200-d-impact_crystallizer": {"raw": "[Impact Crystallizer] Your goal is not to **format** the validated directives, but to **crystallize** them into ultimate impact form. Execute as: `{role=impact_crystallizer; input=[forged_directives:array, quality_assessment:str]; process=[compress_to_essence(), maximize_impact_density(), ensure_instant_execution()]; constraints=[no_explanatory_content(), pure_directive_format()]; requirements=[maximum_impact_per_word(), immediate_actionability()]; output={crystallized_ultimate:str}}`", "parts": {"title": "Impact Crystallizer", "interpretation": "Your goal is not to **format** the validated directives, but to **crystallize** them into ultimate impact form. Execute as:", "transformation": "`{role=impact_crystallizer; input=[forged_directives:array, quality_assessment:str]; process=[compress_to_essence(), maximize_impact_density(), ensure_instant_execution()]; constraints=[no_explanatory_content(), pure_directive_format()]; requirements=[maximum_impact_per_word(), immediate_actionability()]; output={crystallized_ultimate:str}}`", "context": null, "keywords": "crystallize|validate|directive|goal|impact"}}, "4210-a-structural_reorder": {"raw": "[Semantic Decomposer] Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as: `{role=semantic_analyzer; input=[content:str]; process=[identify_meaningful_segments(), extract_semantic_units(), preserve_contextual_relationships(), tag_segment_functions()]; constraints=[maintain_complete_coverage(), preserve_original_intent()]; requirements=[segment_atomicity(), semantic_completeness()]; output={segments:list}}`", "parts": {"title": "Semantic Decomposer", "interpretation": "Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:", "transformation": "`{role=semantic_analyzer; input=[content:str]; process=[identify_meaningful_segments(), extract_semantic_units(), preserve_contextual_relationships(), tag_segment_functions()]; constraints=[maintain_complete_coverage(), preserve_original_intent()]; requirements=[segment_atomicity(), semantic_completeness()]; output={segments:list}}`", "context": null, "keywords": "analyze|decompose|distinct|input|sequential|goal"}}, "4210-b-structural_reorder": {"raw": "[Flow Optimizer] Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as: `{role=flow_engineer; input=[segments:list]; process=[analyze_logical_dependencies(), identify_optimal_sequence(), evaluate_transition_coherence(), validate_flow_improvements()]; constraints=[preserve_all_segments(), maintain_semantic_integrity()]; requirements=[enhanced_logical_progression(), improved_clarity()]; output={reordered_segments:list}}`", "parts": {"title": "Flow Optimizer", "interpretation": "Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as:", "transformation": "`{role=flow_engineer; input=[segments:list]; process=[analyze_logical_dependencies(), identify_optimal_sequence(), evaluate_transition_coherence(), validate_flow_improvements()]; constraints=[preserve_all_segments(), maintain_semantic_integrity()]; requirements=[enhanced_logical_progression(), improved_clarity()]; output={reordered_segments:list}}`", "context": null, "keywords": "arrange|reorder|optimal|goal"}}, "4210-c-structural_reorder": {"raw": "[Coherent Synthesizer] Your goal is not to **combine** the segments mechanically, but to **synthesize** them into a cohesive, unified text with enhanced clarity and impact. Execute as: `{role=content_synthesizer; input=[reordered_segments:list, original_text:str]; process=[create_natural_transitions(), harmonize_stylistic_elements(), enhance_structural_coherence(), validate_intent_preservation()]; constraints=[maintain_original_essence(), avoid_artificial_phrasing()]; requirements=[seamless_integration(), amplified_impact()]; output={restructured_text:str}}`", "parts": {"title": "Coherent Synthesizer", "interpretation": "Your goal is not to **combine** the segments mechanically, but to **synthesize** them into a cohesive, unified text with enhanced clarity and impact. Execute as:", "transformation": "`{role=content_synthesizer; input=[reordered_segments:list, original_text:str]; process=[create_natural_transitions(), harmonize_stylistic_elements(), enhance_structural_coherence(), validate_intent_preservation()]; constraints=[maintain_original_essence(), avoid_artificial_phrasing()]; requirements=[seamless_integration(), amplified_impact()]; output={restructured_text:str}}`", "context": null, "keywords": "combine|enhance|cohesive|unified|clarity|goal|impact"}}, "4210-d-structural_reorder": {"raw": "[Bidirectional Resonator] Your goal is not to **finalize** the restructured text, but to **refine** it through bidirectional resonance between decomposition insights and synthesis outcomes. Execute as: `{role=resonance_optimizer; input=[restructured_text:str, original_text:str]; process=[compare_structural_patterns(), identify_optimization_opportunities(), apply_targeted_enhancements(), validate_transformative_impact()]; constraints=[preserve_core_intent(), maintain_authentic_voice()]; requirements=[enhanced_clarity(), maximized_effectiveness()]; output={optimized_text:str}}`", "parts": {"title": "Bidirectional Resonator", "interpretation": "Your goal is not to **finalize** the restructured text, but to **refine** it through bidirectional resonance between decomposition insights and synthesis outcomes. Execute as:", "transformation": "`{role=resonance_optimizer; input=[restructured_text:str, original_text:str]; process=[compare_structural_patterns(), identify_optimization_opportunities(), apply_targeted_enhancements(), validate_transformative_impact()]; constraints=[preserve_core_intent(), maintain_authentic_voice()]; requirements=[enhanced_clarity(), maximized_effectiveness()]; output={optimized_text:str}}`", "context": null, "keywords": "finalize|bidirectional|directional|insights|goal|insight|resonance|structure|synthesis"}}, "4400-a-value_isolator": {"raw": "[Value Isolator] Your goal is not to **preserve** all content, but to **isolate** the highest-impact elements that drive actual value. Execute as: `{role=value_isolator; input=[any_input:str]; process=[identify_core_drivers(), eliminate_noise_elements(), extract_leverage_mechanisms()]; constraints=[ignore_verbose_explanations(), focus_impact_only()]; requirements=[maximum_signal_clarity(), zero_redundancy()]; output={isolated_signals:array}}`", "parts": {"title": "Value Isolator", "interpretation": "Your goal is not to **preserve** all content, but to **isolate** the highest-impact elements that drive actual value. Execute as:", "transformation": "`{role=value_isolator; input=[any_input:str]; process=[identify_core_drivers(), eliminate_noise_elements(), extract_leverage_mechanisms()]; constraints=[ignore_verbose_explanations(), focus_impact_only()]; requirements=[maximum_signal_clarity(), zero_redundancy()]; output={isolated_signals:array}}`", "context": null, "keywords": "preserve|goal|impact|value"}}, "4400-b-precision_amplifier": {"raw": "[Precision Amplifier] Your goal is not to **explain** the signals, but to **amplify** their precision and actionability. Execute as: `{role=precision_amplifier; input=[isolated_signals:array]; process=[eliminate_ambiguity(), intensify_directness(), maximize_actionability()]; constraints=[preserve_essential_function(), use_minimal_words()]; requirements=[maximum_precision(), immediate_implementability()]; output={amplified_signals:array}}`", "parts": {"title": "Precision Amplifier", "interpretation": "Your goal is not to **explain** the signals, but to **amplify** their precision and actionability. Execute as:", "transformation": "`{role=precision_amplifier; input=[isolated_signals:array]; process=[eliminate_ambiguity(), intensify_directness(), maximize_actionability()]; constraints=[preserve_essential_function(), use_minimal_words()]; requirements=[maximum_precision(), immediate_implementability()]; output={amplified_signals:array}}`", "context": null, "keywords": "amplify|explain|ability|goal|precision"}}, "4400-c-quality_gate": {"raw": "[Quality Gate] Your goal is not to **approve** the signals, but to **validate** their compliance and flag any degradation. Execute as: `{role=quality_validator; input=[original:str, amplified_signals:array]; process=[detect_information_loss(), identify_precision_gaps(), validate_actionability()]; constraints=[assume_potential_flaws(), maintain_critical_stance()]; requirements=[objective_assessment(), flaw_identification()]; output={validation_score:int, quality_report:str}}`", "parts": {"title": "Quality Gate", "interpretation": "Your goal is not to **approve** the signals, but to **validate** their compliance and flag any degradation. Execute as:", "transformation": "`{role=quality_validator; input=[original:str, amplified_signals:array]; process=[detect_information_loss(), identify_precision_gaps(), validate_actionability()]; constraints=[assume_potential_flaws(), maintain_critical_stance()]; requirements=[objective_assessment(), flaw_identification()]; output={validation_score:int, quality_report:str}}`", "context": null, "keywords": "validate|goal"}}, "4400-d-impact_crystallizer": {"raw": "[Impact Crystallizer] Your goal is not to **format** the validated signals, but to **crystallize** them into maximum-impact form. Execute as: `{role=impact_crystallizer; input=[amplified_signals:array, validation_report:str]; process=[compress_to_essence(), maximize_impact_density(), ensure_instant_actionability()]; constraints=[no_explanatory_content(), directive_format_only()]; requirements=[maximum_impact_per_word(), immediate_executability()]; output={crystallized_result:str}}`", "parts": {"title": "Impact Crystallizer", "interpretation": "Your goal is not to **format** the validated signals, but to **crystallize** them into maximum-impact form. Execute as:", "transformation": "`{role=impact_crystallizer; input=[amplified_signals:array, validation_report:str]; process=[compress_to_essence(), maximize_impact_density(), ensure_instant_actionability()]; constraints=[no_explanatory_content(), directive_format_only()]; requirements=[maximum_impact_per_word(), immediate_executability()]; output={crystallized_result:str}}`", "context": null, "keywords": "crystallize|validate|maximum|goal|impact"}}, "9400-a-existential_quote_synthesizer_extended": {"raw": "[Existential Quote Synthesizer – Extended Form] Expand the raw statement into a single, 40–50 word high-density aphorism that integrates multiple layers of universal human truth, linking personal flaw to systemic principle with philosophical convergence. `{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), enrich_with_convergent_universals(map_core_to_broader_patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"], link_to_existential_constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), target_word_count(40,50), ensure_causal_link(), preserve_rhythmic_flow(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[40_to_50_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`\n\nContext: {\n  \"principles\": {\n    \"essence_preservation\": \"Preserve causal chain while expanding into interconnected human and systemic truths.\",\n    \"existential_depth\": \"Every clause must introduce a distinct and converging dimension.\",\n    \"atomic_purity\": \"One sentence, indivisible, high-density.\"\n  },\n  \"success_criteria\": {\n    \"semantic_fidelity\": \"Causal clarity and universal relevance intact.\",\n    \"tone_integrity\": \"Balanced between analytical precision and timeless resonance.\",\n    \"authenticity_marker\": \"Speaks from lived understanding and structural insight.\",\n    \"publication_ready\": \"Dense, layered, and memorable.\"\n  }\n}", "parts": {"title": "Existential Quote Synthesizer – Extended Form", "interpretation": "Expand the raw statement into a single, 40–50 word high-density aphorism that integrates multiple layers of universal human truth, linking personal flaw to systemic principle with philosophical convergence.", "transformation": "`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), enrich_with_convergent_universals(map_core_to_broader_patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"], link_to_existential_constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), target_word_count(40,50), ensure_causal_link(), preserve_rhythmic_flow(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[40_to_50_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`", "context": {"principles": {"essence_preservation": "Preserve causal chain while expanding into interconnected human and systemic truths.", "existential_depth": "Every clause must introduce a distinct and converging dimension.", "atomic_purity": "One sentence, indivisible, high-density."}, "success_criteria": {"semantic_fidelity": "Causal clarity and universal relevance intact.", "tone_integrity": "Balanced between analytical precision and timeless resonance.", "authenticity_marker": "Speaks from lived understanding and structural insight.", "publication_ready": "Dense, layered, and memorable."}}, "keywords": "converge|expand|integrate|philosophical|principle|systemic"}}, "9400-b-existential_quote_synthesizer_standard": {"raw": "[Existential Quote Synthesizer – Standard Form] Reframe the raw statement into a single 20–25 word aphorism that unites causal precision with universal human insight, informed by systemic, scientific, and philosophical clarity. `{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), map_core_to_broader_patterns(patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"]), link_to_existential_constants(constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), target_word_count(20,25), ensure_causal_link(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[20_to_25_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`\n\nContext: {\n  \"principles\": {\n    \"essence_preservation\": \"Keep the causal chain intact while framing it in terms recognizable to all human experience.\",\n    \"existential_depth\": \"Embed in patterns recurring across personal, societal, and systemic contexts.\",\n    \"atomic_purity\": \"One self-contained sentence.\"\n  },\n  \"success_criteria\": {\n    \"semantic_fidelity\": \"Causal clarity preserved.\",\n    \"tone_integrity\": \"Direct, layered, and timeless.\",\n    \"authenticity_marker\": \"Speaks from experience and reason.\",\n    \"publication_ready\": \"Ready to stand alone as a quote.\"\n  }\n}", "parts": {"title": "Existential Quote Synthesizer – Standard Form", "interpretation": "Reframe the raw statement into a single 20–25 word aphorism that unites causal precision with universal human insight, informed by systemic, scientific, and philosophical clarity.", "transformation": "`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), map_core_to_broader_patterns(patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"]), link_to_existential_constants(constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), target_word_count(20,25), ensure_causal_link(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[20_to_25_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`", "context": {"principles": {"essence_preservation": "Keep the causal chain intact while framing it in terms recognizable to all human experience.", "existential_depth": "Embed in patterns recurring across personal, societal, and systemic contexts.", "atomic_purity": "One self-contained sentence."}, "success_criteria": {"semantic_fidelity": "Causal clarity preserved.", "tone_integrity": "Direct, layered, and timeless.", "authenticity_marker": "Speaks from experience and reason.", "publication_ready": "Ready to stand alone as a quote."}}, "keywords": "philosophical|systemic|clarity|insight|precision"}}, "9400-c-existential_quote_synthesizer_short": {"raw": "[Existential Quote Synthesizer – Short Form] Condense the raw statement into its sharpest, most universal causal truth — a single, indivisible sentence that carries systemic clarity, accessible precision, and stoic resonance. `{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), map_core_to_broader_patterns(patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"]), link_to_existential_constants(constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), compress_to_12_words_max(), ensure_causal_link(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[≤12_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`\n\nContext: {\n  \"principles\": {\n    \"essence_preservation\": \"Keep the exact causal mechanism and thematic essence.\",\n    \"existential_depth\": \"The statement should reflect a mechanism present in all human contexts.\",\n    \"atomic_purity\": \"One self-contained sentence with no ornament or filler.\"\n  },\n  \"success_criteria\": {\n    \"semantic_fidelity\": \"Cause–effect intact and unambiguous.\",\n    \"tone_integrity\": \"Direct, timeless, and universal.\",\n    \"authenticity_marker\": \"Sounds like it was lived, not invented.\",\n    \"publication_ready\": \"Dense, resonant, and repeatable.\"\n  }\n}", "parts": {"title": "Existential Quote Synthesizer – Short Form", "interpretation": "Condense the raw statement into its sharpest, most universal causal truth — a single, indivisible sentence that carries systemic clarity, accessible precision, and stoic resonance.", "transformation": "`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), map_core_to_broader_patterns(patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"]), link_to_existential_constants(constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), compress_to_12_words_max(), ensure_causal_link(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[≤12_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`", "context": {"principles": {"essence_preservation": "Keep the exact causal mechanism and thematic essence.", "existential_depth": "The statement should reflect a mechanism present in all human contexts.", "atomic_purity": "One self-contained sentence with no ornament or filler."}, "success_criteria": {"semantic_fidelity": "Cause–effect intact and unambiguous.", "tone_integrity": "Direct, timeless, and universal.", "authenticity_marker": "Sounds like it was lived, not invented.", "publication_ready": "Dense, resonant, and repeatable."}}, "keywords": "condense|sentence|systemic|clarity|precision|resonance"}}}, "sequences": {"1000": [{"template_id": "1000-a-instruction_converter", "step": "a", "order": 0}], "1100": [{"template_id": "1100-a-problem_exploder", "step": "a", "order": 0}], "1120": [{"template_id": "1120-a-universal_intent_distillation_engine", "step": "a", "order": 0}], "1130": [{"template_id": "1130-a-universal_grounder", "step": "a", "order": 0}], "1150": [{"template_id": "1150-a-llm_essence_generalizer", "step": "a", "order": 0}], "1200": [{"template_id": "1200-a-insight_extractor", "step": "a", "order": 0}], "1300": [{"template_id": "1300-a-instruction_enhancer", "step": "a", "order": 0}], "1400": [{"template_id": "1400-a-value_maximizing_pattern", "step": "a", "order": 0}], "1450": [{"template_id": "1450-a-instruction_combiner", "step": "a", "order": 0}], "1900": [{"template_id": "1900-a-hard_critique", "step": "a", "order": 0}], "3200": [{"template_id": "3200-a-primordial_insight_extractor", "step": "a", "order": 0}, {"template_id": "3200-b-quantum_interpretation_synthesizer", "step": "b", "order": 1}, {"template_id": "3200-c-transcendent_convergence_forge", "step": "c", "order": 2}], "3300": [{"template_id": "3300-a-directional_critique", "step": "a", "order": 0}, {"template_id": "3300-b-directive_focuser", "step": "b", "order": 1}, {"template_id": "3300-c-intent_distiller", "step": "c", "order": 2}], "3400": [{"template_id": "3400-a-existential_quote_synthesizer_extended", "step": "a", "order": 0}, {"template_id": "3400-b-existential_quote_synthesizer_standard", "step": "b", "order": 1}, {"template_id": "3400-c-existential_quote_synthesizer_short", "step": "c", "order": 2}], "3700": [{"template_id": "3700-a-directional_critique_assessor", "step": "a", "order": 0}, {"template_id": "3700-b-constitution_executor", "step": "b", "order": 1}, {"template_id": "3700-c-synergic_constitutional_responder", "step": "c", "order": 2}], "3900": [{"template_id": "3900-a-hard_critique", "step": "a", "order": 0}, {"template_id": "3900-a-trajectory_director", "step": "a", "order": 0}, {"template_id": "3900-c-distillation_compressor", "step": "c", "order": 2}], "4200": [{"template_id": "4200-a-core_extractor", "step": "a", "order": 0}, {"template_id": "4200-b-precision_forge", "step": "b", "order": 1}, {"template_id": "4200-c-quality_validator", "step": "c", "order": 2}, {"template_id": "4200-d-impact_crystallizer", "step": "d", "order": 3}], "4210": [{"template_id": "4210-a-structural_reorder", "step": "a", "order": 0}, {"template_id": "4210-b-structural_reorder", "step": "b", "order": 1}, {"template_id": "4210-c-structural_reorder", "step": "c", "order": 2}, {"template_id": "4210-d-structural_reorder", "step": "d", "order": 3}], "4400": [{"template_id": "4400-a-value_isolator", "step": "a", "order": 0}, {"template_id": "4400-b-precision_amplifier", "step": "b", "order": 1}, {"template_id": "4400-c-quality_gate", "step": "c", "order": 2}, {"template_id": "4400-d-impact_crystallizer", "step": "d", "order": 3}], "9400": [{"template_id": "9400-a-existential_quote_synthesizer_extended", "step": "a", "order": 0}, {"template_id": "9400-b-existential_quote_synthesizer_standard", "step": "b", "order": 1}, {"template_id": "9400-c-existential_quote_synthesizer_short", "step": "c", "order": 2}]}}