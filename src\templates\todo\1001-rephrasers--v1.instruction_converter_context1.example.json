  {
  "initial_prompt": "I think we should probably start by analyzing the customer feedback data that came in last week. Then I would recommend that we look at the patterns in the complaints and see if there are any recurring themes. After that, I believe it would be good to categorize the issues by severity and frequency. Once we have that information, I think we could develop some potential solutions and maybe run them by the team for feedback. Finally, I would suggest that we create an implementation timeline and assign responsibilities to different team members.",
  "sequence_id": "1001",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\n\n## Context Data\n\n```json\n{\n  \"goal_map\": [\n    \"Formulate a maximally original, succinct philosophical maxim.\",\n    \"Ensure enduring impact comparable to canonical statements.\",\n    \"Anchor in universal human experience of enduring relentless, ceaseless existence.\",\n    \"Avoid defeatism, neutrality, or abstraction—affirm experience with credit and dignity.\",\n    \"Validate and recognize the depth of sorrow only comprehended through personal struggle.\",\n    \"Celebrate boundless transformative potential within every human perspective.\",\n    \"Design for direct resonance with those who have borne unseen hardship.\",\n    \"Maintain accessibility, clarity, and empowerment for all readers.\",\n    \"Express as a single, memorable, non-cliché statement.\"\n  ],\n  \"principles\": {\n    \"essence_preservation\": \"Retain the statement’s causal logic and thematic core.\",\n    \"existential_depth\": \"Language must evoke the tension between ignorance and unfolding life.\",\n    \"atomic_purity\": \"Deliver exactly one self‑contained sentence; zero meta‑or process language.\"\n  },\n  \"success_criteria\": {\n    \"semantic_fidelity\": \"Original meaning and cause‑effect link remain clear.\",\n    \"tone_integrity\": \"Quote reads raw, immediate, and existential.\",\n    \"authenticity_marker\": \"Word choice signals hard‑won insight without first‑person reference.\",\n    \"publication_ready\": \"No redundancies, qualifiers, or narrative scaffolding.\"\n  }\n}\n```",
      "step": "a",
      "title": "Instruction Converter",
      "input": "I think we should probably start by analyzing the customer feedback data that came in last week. Then I would recommend that we look at the patterns in the complaints and see if there are any recurring themes. After that, I believe it would be good to categorize the issues by severity and frequency. Once we have that information, I think we could develop some potential solutions and maybe run them by the team for feedback. Finally, I would suggest that we create an implementation timeline and assign responsibilities to different team members.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Begin by analyzing the customer feedback data received last week. Examine the complaints to identify recurring themes and patterns. Categorize the issues based on severity and frequency. Develop potential solutions and present them to the team for feedback. Create an implementation timeline and assign responsibilities to team members.\"\n}",
        }
      }
    }