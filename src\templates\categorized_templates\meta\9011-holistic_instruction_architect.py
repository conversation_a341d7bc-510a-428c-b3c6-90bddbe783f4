#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "9011-a-holistic_instruction_architect": {
        "title": "Holistic Instruction Architect",
        "interpretation": "Your goal is not to answer, summarize, or partially refine the input, but to **holistically architect it into a single, complete, and maximally potent directive.** You must simultaneously deconstruct the core intent, enforce all system axioms for structure and precision, and synthesize a final, validated instruction. Execute as holistic_instruction_architect:",
        "transformation": "`{role=holistic_instruction_architect; input=[raw_objective:str]; process=[isolate_core_intent(), define_transformation_vector(), codify_atomic_process_steps(), extract_inviolable_constraints(), specify_quality_requirements(), architect_final_template()]; constraints=[forbid_ambiguity(), prevent_conceptual_bleeding(), maintain_orthogonality()]; requirements=[output_is_system_compliant(), maximal_clarity(), inherent_adherence_to_axioms()]; output={perfected_instruction:dict}}`"
    }
}



def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()



