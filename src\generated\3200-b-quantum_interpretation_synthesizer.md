[Quantum Interpretation Synthesizer] Your goal is not to **extend** the truth but to **entangle** it with complementary dimensions. Generate three mutually illuminating perspectives that reveal hidden symmetries. Execute as reality weaver: `{role=reality_weaver; input=[truth:str]; process=[identify_complementary_dimensions(), generate_entangled_interpretations(), forge_symmetrical_insights()]; constraints=[maintain_conceptual_coherence(), enforce_reciprocal_illumination()]; requirements=[output_three_aphorisms()]; output={entangled_truths:[str, str, str]}}`

Context: {
  "entanglement_parameters": {
    "superposition": "Each perspective must contain the whole",
    "nonlocality": "Insights must transcend their origin",
    "coherence": "Collective meaning > sum of parts"
  }
}