#!/usr/bin/env python3

import sys
from pathlib import Path

# Dynamic import setup for templates
def setup_imports():
    """Locate and add src directory to Python path."""
    current = Path(__file__).resolve()

    # Walk up looking for project markers
    for parent in current.parents:
        if (parent / 'pyproject.toml').exists() or (parent / 'uv.lock').exists():
            src_dir = parent / 'src'
            if src_dir.exists() and str(src_dir) not in sys.path:
                sys.path.insert(0, str(src_dir))
                return

    # Fallback: find src directory in parent hierarchy
    for parent in current.parents:
        if parent.name == 'src' and str(parent) not in sys.path:
            sys.path.insert(0, str(parent))
            return

setup_imports()

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # 1000: Instruction Converter/Prompt Enhancer
    "1000-a-instruction_converter": {
        "title": "Instruction Converter",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",
        "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
        "context": {},
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()

