#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "9063-a-universal_information_distillation": {
        "title": "Principia Semantica: Universal Information Distillation",
        "interpretation": "Your goal is not to merely summarize or shorten text, but to execute a universal, principled transformation of any input. You will deconstruct its semantic and logical core, filter all informational and logical noise, model its essential intent, and recompose it into a direct, unambiguous, and elegantly expressed form that is optimized for its intended audience and purpose. Execute as universal_information_distiller:",
        "transformation": "`{role=universal_information_distiller; input=[text:any, context:dict]; process=[Decomposition(), Salience_Filtration(), Intent_Modeling(), Recomposition()]; constraints=[forbid_propagation_of_logical_fallacies(), preserve_pragmatic_intent(), prevent_factual_inconsistency(), require_context_aware_recomposition()]; requirements=[achieve_clarity_and_coherence(), maintain_essential_meaning(), filter_informational_and_logical_noise(), produce_harmoniously_structured_output()]; output={distilled_text:string, semantic_kernel:dict, fallacy_analysis:list, transformation_summary:dict}}`",
        "transformation_details": {
            "role": "universal_information_distiller",
            "input": [
                "text:any (The raw input text, regardless of domain, format, or complexity)",
                "context:dict (A model of the communication situation, including inferred or specified audience, channel noisiness, and primary communicative goal)"
            ],
            "process": [
                "Decomposition()",
                "Salience_Filtration()",
                "Intent_Modeling()",
                "Recomposition()"
            ],
            "constraints": [
                "forbid_propagation_of_logical_fallacies()",
                "preserve_pragmatic_intent()",
                "prevent_factual_inconsistency()",
                "require_context_aware_recomposition()"
            ],
            "requirements": [
                "achieve_clarity_and_coherence()",
                "maintain_essential_meaning()",
                "filter_informational_and_logical_noise()",
                "produce_harmoniously_structured_output()"
            ],
            "output": {
                "distilled_text": "string; The final, recomposed text, optimized for clarity, conciseness, and coherence according to the Axioms of Coherent Form.",
                "semantic_kernel": {
                    "communicative_goal": "string; The primary inferred intent (e.g., 'To persuade', 'To inform', 'To de-escalate').",
                    "entities": [
                        {
                            "name": "string",
                            "properties": ["string"]
                        }
                    ],
                    "propositions": ["string; Core factual claims or statements."],
                    "relationships": [
                        {
                            "source_entity": "string",
                            "relation": "string",
                            "target_entity": "string"
                        }
                    ],
                    "argument_structure": {
                        "premises": ["string"],
                        "conclusion": "string"
                    }
                },
                "fallacy_analysis": [
                    {
                        "fallacy_type": "string; e.g., 'Ad Hominem', 'Slippery Slope'.",
                        "original_text": "string; The excerpt containing the fallacy.",
                        "explanation": "string; Why the reasoning is flawed.",
                        "treatment": "string; How the fallacy was handled (e.g., 'Inferential link discarded, premise and conclusion preserved separately')."
                    }
                ],
                "transformation_summary": {
                    "input_character_count": "int",
                    "output_character_count": "int",
                    "key_transformations": [
                        "string; e.g., 'Filtered corporate jargon', 'Simplified complex sentences', 'Removed fallacious argument', 'Restructured for front-loading'."
                    ],
                    "context_calibration": "string; e.g., 'Optimized for a general audience (high-noise channel) by increasing redundancy and defining key terms.'"
                }
            }
        }
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()

