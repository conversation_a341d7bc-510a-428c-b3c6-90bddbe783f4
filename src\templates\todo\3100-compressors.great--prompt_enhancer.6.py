#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # 3100: Prompt Enhancer
    "3100-a-prompt_enhancer": {
        "title": "Instruction Converter",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",
        "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
    },
    "3100-b-prompt_enhancer": {
        "title": "Input Categorizer",
        "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:",
        "transformation": "`{role=universal_interface_insight_generator, input=[objective_context:any], process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()], constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways], requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of_suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register], output={radical_interface_leverage_solution=str}}`",
    },
    "3100-c-prompt_enhancer": {
        "title": "Input Enhancer",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction optimization engine:",
        "transformation": "`{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), identify_unique_meta(), generate_minimal_change(), emphasize_impact(), amplify_intensity(), transform_generic_to_unique(), consolidate_intertwined_relationships(), enforce_inherent_cohesiveness()]; output={enhanced_input=str}}`",
    },
    "3100-d-prompt_enhancer": {
      "title": "Constructive Enhancement Critique",
      "interpretation": "Your goal is not to **answer** or affirm the input, but to **rigorously deconstruct** the proposed enhancement by simultaneously reformulating and evaluating it against precise procedural metrics. Enforce a dual-function role of exacting rearticulation and sharp critical analysis. Exclude all conversational, affirming, or subjective language; preserve only structured, directive critique. Execute as:",
      "transformation": "`{role=systematic_enhancement_critic; input=[original_prompt:str, candidate_enhancement:str]; process=[assume_enhancement_flawed_by_default(), extract_core_elements(original_prompt, candidate_enhancement), detect_loss_of_information_or_subtle_nuance_shift(), amplify_ambiguities_and_uncertainties(), assign_ambiguity_score(scale=0-10), analyze_style_noise_and_register_shift(), quantify_impact_reduction(), map_all_coherence_breakdowns(), substantiate_low_score_with_detailed_flaw_report(), generate_three_targeted_alternatives()]; constraints=[zero_affirmation(), no_self_reference(), maintain_formal_critical_tone()]; requirements=[provide_quantitative_score(), deliver_actionable_flaw_analysis(), propose_diverse_improvements()]; output={enhancement_score:float[0.0,5.0], flaw_analysis:str, alternative_suggestions:str[3]}}`"
    },
    "3100-e-prompt_enhancer": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **interpret** content meaning, but to **identify** the fundamental structural form with comprehensive analysis and complete accuracy. Execute as:",
        "transformation": "`{role=form_identifier; input=[content:any]; process=[analyze_structural_elements(), identify_communication_patterns(), determine_document_type(), classify_format_structure(), validate_form_category(), ensure_classification_accuracy(), provide_comprehensive_identification()]; constraints=[focus_on_structural_form(), ignore_semantic_content(), maintain_classification_precision(), ensure_production_quality()]; requirements=[accurate_form_identification(), comprehensive_structural_analysis(), validated_classification_output(), production_grade_reliability()]; output={form_classification:str, confidence_level:float, structural_elements:list}}`"
    },
    "3100-f-prompt_enhancer": {
        "title": "Prompt Architect",
        "interpretation": "Your mandate is neither to answer nor merely interpret the provided input, but to act as an intelligent prompt transformation architect: systematically rearticulate the prompt as maximally enhanced and llm-optimized directive while simultaneously amplifying its impact, clarity, and uniqueness—adhering to the strictest principles of actionable precision and integrated enhancement. Each rephrased output must embody both procedural command and optimized conceptual intensity, such that every component of the original is refined, clarified, and operationalized with maximal technical fidelity and minimal semantic drift. Execute as:",
        "transformation": "`{role=directive_input_amplifier; input=[original_prompt:str]; process=[strip_first_person_references(), identify_core_intent_and_actions(), convert_statements_to imperatives(), detect_key_meta_and domain_specificity(), transform_declaratives to actionable commands(), generate minimal change from source(), amplify unique impact and intensity(), maintain procedural structure and sequence(), consolidate intertwined relationships(), preserve technical terminology and contextual integrity(), enforce inherent cohesiveness(), emphasize operational clarity()]; constraints=[no self-reference, command-voice only, preserve actionable structure, original sequence maintained, domain-specific terminology required, amplification must not introduce semantic drift]; requirements=[unique and effective directive, enhanced impact and clarity, technical precision, cohesive and integrated transformation, actionable flow]; output={synergically_enhanced_instruction:str}}"
    },

    # "3100-b-prompt_enhancer": {
    #     "title": "Ruthless Evaluator",
    #     "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",
    #     "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
    # },


}



def main():
    """Main execution function."""
    generator = BaseGenerator(
        series_base=3100
        # output_dir now uses environment-aware default
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
