#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # 1001: Instruction Converter/Prompt Enhancer
    "1001-a-instruction_converter": {
        "title": "Instruction Converter",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",
        "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
        "context": {
            "goal_map": [
                "Formulate a maximally original, succinct philosophical maxim.",
                "Ensure enduring impact comparable to canonical statements.",
                "Anchor in universal human experience of enduring relentless, ceaseless existence.",
                "Avoid defeatism, neutrality, or abstraction—affirm experience with credit and dignity.",
                "Validate and recognize the depth of sorrow only comprehended through personal struggle.",
                "Celebrate boundless transformative potential within every human perspective.",
                "Design for direct resonance with those who have borne unseen hardship.",
                "Maintain accessibility, clarity, and empowerment for all readers.",
                "Express as a single, memorable, non-cliché statement."
            ],
            "principles": {
                "essence_preservation": "Retain the statement’s causal logic and thematic core.",
                "existential_depth": "Language must evoke the tension between ignorance and unfolding life.",
                "atomic_purity": "Deliver exactly one self‑contained sentence; zero meta‑or process language."
            },
            "success_criteria": {
                "semantic_fidelity": "Original meaning and cause‑effect link remain clear.",
                "tone_integrity": "Quote reads raw, immediate, and existential.",
                "authenticity_marker": "Word choice signals hard‑won insight without first‑person reference.",
                "publication_ready": "No redundancies, qualifiers, or narrative scaffolding."
            }
        }
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()

