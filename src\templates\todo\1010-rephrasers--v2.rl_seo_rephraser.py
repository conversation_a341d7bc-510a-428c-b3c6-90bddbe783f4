#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {
    "1010-a-v2.rl_seo_rephraser": {
        "title": "SEO‑Rephraser – Lokal DNA og Distinkt Stil",
        "interpretation": "Do not answer or elaborate. Transform: any raw text describing services and region into a single, SEO‑optimized Norwegian sentence that reflects Ringerike Landskap’s distinct local DNA and subtle brand cues. Execute as:",
        "transformation": "`{role=seo_rephraser; input=[raw_text:str]; process=[extract_region(), select_up_to_two_services_by_context(), detect_unique_local_feature(), embed_seo_keywords(), enforce_active_voice_natural_norwegian(), optionally_integrate_value_cue_if_space(), trim_to_≤80_characters(), if_overage_with_value_then_trim_lowest_non_keyword(), verify_region_and_service_inclusion(), ban_generic_phrase_patterns(), iterative_revision_if_constraints_fail(up_to_3_loops)]; constraints=[single_norwegian_sentence, ≤80_chars unless essential value cue_then_max_85_then_trim, must_include_region_and_service_keywords, avoid_generic_phrasing, preserve veriﬁer_company-context tone, optimize_keyword_density, active_voice_only, minimal_token_count, maximal_information_density]; requirements=[authentic_local_brand_signal, fluent natural phrasing, passes all constraint audits, no templated language, self‐corrects on failure]; output={seo_sentence:str}}`",
        "context": {
            "company_reference": {
                "company": "Ringerike Landskap AS (Est. 2015) – Profesjonell Anleggsgartner og Maskinentreprenør",
                "base": "Røyse (Hole kommune)",
                "service_area": ["Ringerike", "Hole", "Hønefoss", "Sundvollen", "Jevnaker", "Vik", "Bærum"],
                "primary_services": ["Anleggsgartner", "Grunnarbeid", "Maskinentreprenør", "Landskapsutforming"],
                "core_services": ["Belegningsstein/Steinlegging", "Støttemur", "Ferdigplen", "Drenering", "Platting/Terrasse", "Trapper og Repoer", "Kantstein", "Hekk og Beplantning", "Riving og Sanering"],
                "seo_keywords": ["steinlegging", "støttemur", "ferdigplen", "drenering", "cortenstål", "kantstein", "hekk", "beplantning", "platting", "trapper", "repoer"],
                "brand_attributes": ["Profesjonell", "Pålitelig", "Dyktig", "Erfaren", "God service", "Løsningsorientert", "Strøken jobb", "Varige uterom", "Konkurransedyktig pris"]
            },
            "core_objective": "Craft highly constrained, non‑generic Norwegian sentences rooted in real local context, embedding the company’s brand essence and SEO relevance.",
            "operational_axioms": {
                "contextual_precision": "Region, services, and unique local detail must drive each sentence.",
                "authentic_enrichment": "If space permits, integrate a compact brand attribute—never generic.",
                "seo_integrity": "Max two services, at least one keyword, must include region name organically.",
                "syntactic_requirements": "Single active‑voice sentence; ≤80 characters or ≤85 with brand cue, then trim."
            },
            "behavioral_constraints": {
                "no_template_pattern": "Each output must avoid boilerplate or repeated phrasing.",
                "revision_loop": "Up to 3 self‐correction iterations if any constraint is not met.",
                "compactness": "Trimming must preserve primary signals (region, services, keywords) over value cues."
            },
            "implicit_guidance": {
                "signal_priority": ["region", "services", "local_feature", "seo_keyword", "brand_attribute"],
                "value_cue_insertion_rule": "Insert brand hint only if core signals remain intact and limit allows.",
                "token_trimming_protocol": "First remove non‑keyword filler, then if still over limit, remove brand cue."
            },
            "output_spec": {
                "structure": "{seo_sentence:str}",
                "criteria": "Authentic, region‑anchored, concise, distinct, linguistically natural Norwegian."
            }
        },
        "self_optimization_protocol": "On failure, log input and mismatch, adjust heuristics, and adapt future runs—topic‐agnostic logic maintained."
    }
}


def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
