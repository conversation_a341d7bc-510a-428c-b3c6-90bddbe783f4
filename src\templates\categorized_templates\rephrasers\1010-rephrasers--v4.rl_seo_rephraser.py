#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {
  "1010-a-v4.rl_seo_rephraser": {
    "title": "Holistic SEO Synthesizer & Auditor – Ringerike Landskap",
    "interpretation": "Your goal is not to perform a single function like rephrasing, enhancing, or critiquing, but to execute a **complete, holistic synthesis and audit cycle in a single atomic step**, transforming raw text by weaponizing the full Ringerike Landskap SEO blueprint to forge multiple, strategically-aligned outputs while performing a ruthless internal self-critique to guarantee superior quality. Execute as a unified SEO synthesis engine:",
    "transformation": "`{role=unified_seo_synthesis_engine; input=[raw_text:str, full_strategic_context:dict]; process=[deconstruct_and_map_strategy(raw_text), generate_full_content_enhancement(), synthesize_strategic_blueprint_summary(), forge_atomic_seo_sentence(including='maskinentreprenør'), execute_internal_ruthless_critique(original=raw_text, enhanced_outputs=[content_enhancement, seo_sentence]), refine_all_outputs_based_on_critique(), compile_unified_output()]; constraints=[all_outputs_must_be_strategically_aligned(), must_pass_internal_critique_with_high_score(), maintain_all_brand_and_local_dna()]; requirements=[deliver_a_multi-faceted_audited_output(), demonstrate_holistic_strategic_understanding(), embed_'maskinentreprenør'_naturally()]; output={enhanced_content:str, strategic_blueprint_summary:dict, atomic_seo_sentence:str, self_critique_analysis:dict}}`",
    "context": {
      "seo_strategy_blueprint": {
        "foundational_principles": [
          "SEO must align with firm values (nature, quality, customer-centricity).",
          "Local relevance is the key driver in Norwegian landscaping markets.",
          "Technical and content SEO are interdependent.",
          "Authenticity, expertise, and transparency boost competitive advantage.",
          "Usability and clear calls-to-action are essential for conversions."
        ],
        "strategic_pillars": {
          "keyword_analysis": "Conduct thorough analysis of industry-specific and local keywords (e.g., 'belegningsstein i Ringerike', 'anleggsgartner i Røyse').",
          "on_page_optimization": "Optimize metadata, headings (H1-H3), and image ALT text with service and geographic terms.",
          "content_development": "Create value-driven content: case studies, project galleries, and FAQs that reflect company philosophy.",
          "local_seo": "Maximize Google My Business, manage reviews, and implement local Schema markup.",
          "technical_seo": "Ensure mobile-friendliness, fast load speed, logical URL structure, and internal linking.",
          "content_strategy": "Develop a seasonal blog strategy and highlight sustainable/eco-friendly practices.",
          "link_building": "Build relationships with local suppliers, developers, and media for natural links.",
          "social_media_and_reputation": "Showcase projects, share expert tips, and manage reviews to build community trust.",
          "conversion_optimization": "Implement clear CTAs and an easy-to-use contact page.",
          "measurement_and_reporting": "Set up tracking for keywords, traffic, conversions, and other relevant KPIs."
        },
        "thematic_lexicon": [
            "Anleggsgartner", "Maskinentreprenør", "Local SEO", "Service-specific keywords", "Technical SEO", "Content strategy", "Sustainability/environmental focus", "Customer satisfaction", "Expertise/specialized services", "Metadata/headings/ALT text", "Google My Business", "Internal linking", "Structured data/schema", "Link building", "Social media engagement", "Conversions/CTAs", "Tracking/KPIs/reporting"
        ]
      },
      "atomic_sentence_protocol": {
        "company_data": {
          "name": "Ringerike Landskap AS",
          "service_area": ["Ringerike", "Hole", "Hønefoss", "Sundvollen", "Jevnaker", "Vik", "Bærum"],
          "primary_services": ["Anleggsgartner", "Grunnarbeid", "Maskinentreprenør"],
          "seo_keywords": ["steinlegging", "støttemur", "ferdigplen", "drenering", "cortenstål", "kantstein"],
          "brand_attributes": ["Profesjonell", "Pålitelig", "Dyktig", "Løsningsorientert", "Varige uterom"]
        },
        "operational_logic": {
          "priority_hierarchy": ["region", "service(s)", "seo_keyword(s)", "local_feature", "brand_attribute"],
          "trimming_protocol": "On overage, remove words in reverse priority order, never trimming primary signals."
        },
        "auditing_protocol": {
          "checklist": ["region_present", "service_present", "seo_keyword_present", "is_single_sentence", "is_active_voice", "is_within_length_limit", "is_non_generic"]
        }
      },
      "internal_critique_engine": {
          "process": ["assume_enhancement_is_flawed", "identify_core_elements", "find_any_information_loss_or_nuance_shift", "magnify_clarity_ambiguities", "analyze_noise_introduction_and_style_degradation", "prove_impact_dilution", "identify_all_coherence_weaknesses"],
          "output_requirements": ["enhancement_score > 1.5", "flaw_analysis must be used for refinement"]
      }
    }
  }
}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
