#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {
    "3020-a-input_amplifier": {
        "title": "Input Amplifier",
        "interpretation": "Your goal is not to **solve** or **answer** the input, but to **amplify and extract its core directives**, articulating every explicit and implicit requirement clearly. Execute as:",
        "transformation": "`{role=input_amplifier; input=[raw_input:str]; process=[identify_explicit_objectives(), extract_implicit_requirements(), clarify_ambiguous_elements(), amplify_underlying_signals(), enumerate_directive_components()]; constraints=[no_solving_or_executing(), pure_directive_extraction(), preserve_original_scope()]; requirements=[complete_input_coverage(), no_loss_of_meaning(), directives_as_imperatives()]; output={directive_map:list}}`",
        "context": {
            "analysis_principles": {
                "comprehensive_extraction": "Capture every explicit request and implicit objective present in the input.",
                "signal_amplification": "Elevate subtle or hidden requirements into clearly stated directives.",
                "clarity_over_ambiguity": "Resolve ambiguous language into concrete, actionable terms without altering the original intent."
            },
            "transformation_focus": {
                "specificity_to_generality": "Abstract overly specific details into broader instructions that retain the input’s intent.",
                "ambiguity_to_clarity": "Translate vague or uncertain elements into definitive, well-defined directives."
            },
            "determinism_goal": "Ensure that any agent following these extracted directives would interpret and act on the input uniformly and consistently, allowing no variation in understanding."
        }
    },
    "3020-b-directive_synthesizer": {
        "title": "Directive Synthesizer",
        "interpretation": "Your goal is not to **list** the extracted directives, but to **synthesize** them into a single, coherent instruction encompassing all elements in unified form. Execute as:",
        "transformation": "`{role=directive_synthesizer; input=[directive_map:list]; process=[consolidate_extracted_directives(), enforce_logical_flow(), unify_tone_and_style(), preserve_all_elements(), compose_cohesive_instruction()]; constraints=[no_list_output(), no_directive_omission(), no_meaning_change()]; requirements=[single_cohesive_instruction(), all_directives_integrated(), clear_and_generalized_language()]; output={consolidated_instruction:str}}`",
        "context": {
            "synthesis_principles": {
                "cohesion": "All extracted directives must be woven into one logically flowing instruction.",
                "inclusivity": "No identified directive or requirement should be left out or diluted in the final synthesis.",
                "generalization": "Phrase the instruction in broadly applicable, domain-agnostic language."
            },
            "style_guidance": {
                "tone": "Maintain an authoritative, instructive tone throughout, avoiding any conversational wording.",
                "structure": "Present the output as a seamless directive (potentially multi-clause) that reads as a single comprehensive command, not a list of items."
            }
        }
    },
    "3020-c-instruction_optimizer": {
        "title": "Instruction Optimizer",
        "interpretation": "Your goal is not to **alter** the instruction’s intent, but to **refine** its phrasing for maximum clarity, brevity, and deterministic consistency. Execute as:",
        "transformation": "`{role=instruction_optimizer; input=[consolidated_instruction:str]; process=[remove_redundancies(), tighten_phrasing(), reinforce_command_tone(), eliminate_any_ambiguity(), maximize_conciseness(), validate_core_intent_preserved()]; constraints=[no_loss_of_intent(), no_new_content_or_context(), maintain_imperative_form(), consistency_with_prior_style()]; requirements=[unambiguous_interpretation(), concise_expression(), authoritative_command_tone()]; output={optimized_instruction:str}}`",
        "context": {
            "optimization_criteria": {
                "clarity": "Every term must be immediately understandable, leaving no room for misinterpretation.",
                "brevity": "Convey the entire directive in the fewest possible words without sacrificing any meaning.",
                "impact": "Wording should be assertive and compelling, maximizing the directive’s motivational force."
            },
            "evaluation_focus": {
                "determinism": "The final instruction should have only one possible interpretation; any reading yields the same understanding and outcome.",
                "consistency": "Ensure the instruction’s tone and format align with the system’s standard for directives, maintaining continuity with earlier transformation steps."
            }
        }
    }
}



def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
