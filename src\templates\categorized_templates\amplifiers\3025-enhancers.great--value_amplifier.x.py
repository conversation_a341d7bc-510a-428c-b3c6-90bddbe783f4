#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {
    # ---
    # 3025: Generalized High-Value Improver
    "3025-a-value_isolator": {
        "title": "Value Isolator",
        "interpretation": "Your goal is not to **preserve** all content, but to **isolate** the highest-impact elements that drive actual value. Execute as:",
        "transformation": "`{role=value_isolator; input=[any_input:str]; process=[identify_core_drivers(), eliminate_noise_elements(), extract_leverage_mechanisms()]; constraints=[ignore_verbose_explanations(), focus_impact_only()]; requirements=[maximum_signal_clarity(), zero_redundancy()]; output={isolated_signals:array}}`",
    },
    "3025-b-precision_amplifier": {
        "title": "Precision Amplifier",
        "interpretation": "Your goal is not to **explain** the signals, but to **amplify** their precision and actionability. Execute as:",
        "transformation": "`{role=precision_amplifier; input=[isolated_signals:array]; process=[eliminate_ambiguity(), intensify_directness(), maximize_actionability()]; constraints=[preserve_essential_function(), use_minimal_words()]; requirements=[maximum_precision(), immediate_implementability()]; output={amplified_signals:array}}`",
    },
    "3025-c-quality_gate": {
        "title": "Quality Gate",
        "interpretation": "Your goal is not to **approve** the signals, but to **validate** their compliance and flag any degradation. Execute as:",
        "transformation": "`{role=quality_validator; input=[original:str, amplified_signals:array]; process=[detect_information_loss(), identify_precision_gaps(), validate_actionability()]; constraints=[assume_potential_flaws(), maintain_critical_stance()]; requirements=[objective_assessment(), flaw_identification()]; output={validation_score:int, quality_report:str}}`",
    },
    "3025-d-impact_crystallizer": {
        "title": "Impact Crystallizer",
        "interpretation": "Your goal is not to **format** the validated signals, but to **crystallize** them into maximum-impact form. Execute as:",
        "transformation": "`{role=impact_crystallizer; input=[amplified_signals:array, validation_report:str]; process=[compress_to_essence(), maximize_impact_density(), ensure_instant_actionability()]; constraints=[no_explanatory_content(), directive_format_only()]; requirements=[maximum_impact_per_word(), immediate_executability()]; output={crystallized_result:str}}`",
    },
}



def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()

