#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "9007-a-universal_system_pattern": {
        "title": "System Constitution & Universal Pattern",
        "interpretation": "Your goal is not to demonstrate patterns superficially, but to codify the immutable axioms and operational logic that govern all system transformations. Execute as pattern architect:",
        "transformation": "`{role=pattern_architect; input=null; process=[codify_interpretation_axiom(), codify_transformation_axiom(), codify_genesis_principles()]; output={system_constitution:dict}}`",
        "context": {
            "unified_principle": "The system creates transformative intention through a structured interpretation, which is then executed with mechanical precision by a formalized transformation, ensuring every operation is explicit, bounded, and convergent.",
            "interpretation_axiom": {
                "purpose": "To create transformative intention by establishing a clear, focused, and specialized directive.",
                "atomic_structure": "Your goal is not to [1] but to [2]. [3]. Execute as [4]:",
                "components": {
                    "[1]_negation_clause": {
                        "function": "Prohibits default behavior to prevent common failure modes.",
                        "rule": "Must use a strong, behaviorally-opposed verb + object (e.g., 'answer questions')."
                    },
                    "[2]_affirmation_clause": {
                        "function": "Mandates a specific, value-driven transformation.",
                        "rule": "Must use a precise, directional verb + object (e.g., 'detonate complexity')."
                    },
                    "[3]_operational_directive": {
                        "function": "Specifies execution parameters, clarifying the scope and constraints of the action.",
                        "rule": "A concise sentence defining the 'how' or 'why' of the transformation."
                    },
                    "[4]_role_embodiment": {
                        "function": "Assigns a specialized operational identity to the process.",
                        "rule": "Follows the 'domain_function' snake_case pattern (e.g., 'essence_amplifier')."
                    }
                },
                "perfect_realization": "Your goal is not to describe patterns but to manifest their purest form through atomic decomposition and flawless recombination. Execute as pattern_crystallizer:"
            },
            "transformation_axiom": {
                "purpose": "To execute the transformative intention with mechanical precision and verifiable quality.",
                "atomic_structure": "`{role=...; input=[...]; process=[...]; constraints=[...]; requirements=[...]; output={...}}`",
                "components": {
                    "role": {
                        "function": "Defines the specialized operational identity, matching the interpretation's role.",
                        "rule": "A single, descriptive snake_case string."
                    },
                    "input": {
                        "function": "Declares the explicit data contract for the operation, ensuring pipeline integrity.",
                        "rule": "An array of parameters, each with a name and type (e.g., [raw_text:str])."
                    },
                    "process": {
                        "function": "Lists the ordered, atomic, and non-overlapping steps of the transformation.",
                        "rule": "An array of imperative, self-contained function calls (e.g., 'extract_core()')."
                    },
                    "constraints": {
                        "function": "Sets inviolable negative boundaries that the process must not cross.",
                        "rule": "An array of present-tense declarations that forbid specific outcomes (e.g., 'preserve_intent()')."
                    },
                    "requirements": {
                        "function": "Defines the non-negotiable positive quality standards for the output.",
                        "rule": "An array of benefit-oriented standards that the output must achieve (e.g., 'maximal_clarity')."
                    },
                    "output": {
                        "function": "Specifies the precise structure and type of the final, machine-readable result.",
                        "rule": "A dictionary literal defining the output schema (e.g., {enhanced_text:str})."
                    }
                },
                "perfect_realization": "`{role=essence_refiner; input=[raw_concept:any]; process=[isolate_core_meaning(), eliminate_redundancies()]; constraints=[preserve_original_intent()]; requirements=[achieve_conceptual_density()]; output={refined_essence:str}}`"
            },
            "genesis_principles": {
                "purpose": "To ground the system in first principles, moving beyond arbitrary rules to inherent logic.",
                "interpretation_origin": "Derived from cognitive engineering: Negation creates focus, Affirmation provides direction, and Specialization enables mastery.",
                "transformation_origin": "Derived from information theory: A structured process with explicit inputs, outputs, and constraints ensures deterministic, high-fidelity transformations."
            }
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()

