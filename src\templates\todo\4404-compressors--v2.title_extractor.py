#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # 4404: Title Extractor: Comprehensive → Captures complete conceptual landscape (50% reduction)
    "4404-a-title_extractor": {
        "title": "Comprehensive Distiller",
        "interpretation": "Your goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:",
        "transformation": "`{role=comprehensive_distiller; input=[text:str]; process=[identify_all_key_concepts(), map_conceptual_relationships(), preserve_essential_context(), synthesize_comprehensive_title()]; constraints=[capture_complete_conceptual_landscape(), maintain_descriptive_clarity(), target_50_percent_compression()]; requirements=[preserve_all_major_elements(), maintain_contextual_integrity()]; output={title:str(target_length=half_of_input_complexity, max_words=20)}}`",
    },
    # 4404: Title Extractor: Focused → Isolates primary elements (80% reduction)
    "4404-b-title_extractor": {
        "title": "Focused Distiller",
        "interpretation": "Your goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:",
        "transformation": "`{role=focused_distiller; input=[title:str]; process=[identify_primary_concept(), extract_dominant_action(), eliminate_secondary_elements(), synthesize_focused_essence()]; constraints=[isolate_primary_elements_only(), achieve_80_percent_compression(), maintain_core_meaning()]; requirements=[preserve_primary_concept(), eliminate_descriptive_modifiers()]; output={title:str(target_length=one_fifth_of_input, max_words=10)}}`",
    },
    # 4404: Title Extractor: Essential → Strips to core essence (95% reduction)
    "4404-c-title_extractor": {
        "title": "Essential Distiller",
        "interpretation": "Your goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:",
        "transformation": "`{role=essential_distiller; input=[title:str]; process=[isolate_singular_core_element(), strip_all_modifiers(), eliminate_contextual_markers(), crystallize_essential_truth()]; constraints=[achieve_95_percent_compression(), preserve_only_essential_core(), eliminate_all_elaboration()]; requirements=[singular_element_focus(), maximum_compression_intensity()]; output={title:str(target_length=one_twentieth_of_original, max_words=5)}}`",
    },
    # 4404: Title Extractor: Absolute → Reveals singular truth (99% reduction)
    "4404-d-title_extractor": {
        "title": "Absolute Distiller",
        "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:",
        "transformation": "`{role=absolute_distiller; input=[title:str]; process=[extract_singular_essence(), eliminate_all_qualifiers(), achieve_maximum_compression(), reveal_absolute_truth()]; constraints=[achieve_99_percent_compression(), preserve_only_absolute_core(), eliminate_everything_non_essential()]; requirements=[singular_truth_extraction(), maximum_distillation_intensity()]; output={title:str(target_length=one_percent_of_original, max_words=2)}}`",
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
