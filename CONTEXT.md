# AI Systems - Instruction Sequence Executor

Multi-step LLM instruction sequence executor with template management system. Execute user prompts through sequences of different system instructions using multiple LLM models for diverse perspectives and step-by-step refinement.

## TL;DR

The system is a template-driven instruction sequence execution engine and multi-LLM orchestration framework that processes user prompts through systematic, standardized, machine-parsable templates with defined components including title, interpretation, transformation, and optional context, supports flexible sequence specifications such as single, multi-step, ranged, combined, keyword, or wildcard patterns, enables multi-step sequential processing with chain mode execution that preserves the original prompt, features an architecture incorporating a core execution engine, template catalog, sequence manager, and LiteLLM integration, offers cost tracking, structured JSON results, and streaming output, provides aggregation to combine multi-step outputs with structured analysis, validates templates for structural and syntactic compliance, allows extensibility through new template series, custom processors, and provider integrations, and delivers universal applicability, composable enhancement, self-improvement, modular extensibility, provider flexibility, and rich interaction via CLI or programmatic interfaces.

- The system is a template-driven instruction sequence execution engine and multi-LLM orchestration framework.
- It processes user prompts through systematic, standardized, machine-parsable templates.
- It supports multi-step sequential processing, chain mode execution, and aggregator-based synthesis.
- Its architecture includes a core execution engine, template catalog, sequence manager, and LiteLLM integration.
- Templates follow a defined structure with title, interpretation, transformation, and optional context.
- Sequences can be specified as single, multi-step, ranged, combined, keyword, or wildcard patterns.
- Execution preserves the original prompt while chaining outputs between steps.
- It supports cost tracking, structured JSON results, and streaming output.
- The aggregation system combines multi-step results and performs structured analysis.
- Template validation enforces structural and syntactic correctness.
- Extensibility includes new template series, custom processors, and provider integrations.
- The system enables universal applicability, composable enhancement, self-improvement, modular extensibility, provider flexibility, and rich interaction via CLI or code.

## Features

- Execute prompts through multiple system instructions
- Support for multiple LLM models per step
- Template-based instruction sequences
- Cost tracking and structured JSON output
- Chain mode for sequential processing
- Aggregator sequences for result synthesis

## Quick Start

```bash
# Install dependencies
uv sync

# Run with default sequence
uv run python src/main.py --prompt "Your question here"

# Use specific sequence and models
uv run python src/main.py --sequence "1000" --models "gpt-4o,claude-3-haiku" --prompt "Your question"
```

## Core Components

- **main.py**: Core execution engine with LLM interaction via litellm
- **templates/**: Instruction template management system with stage-based organization
- **Template Catalog**: Dynamic template discovery and sequence management
- **Chain Mode**: Sequential step processing with output chaining
- **Aggregator**: Result synthesis across multiple steps
- **Cost Tracking**: Integrated cost calculation for API usage
- **Streaming Output**: Asynchronous execution with structured JSON results

## Usage Examples

```bash
# Basic usage with default sequence
uv run python src/main.py --prompt "Analyze this text for key insights"

# Specific sequence and multiple models
uv run python src/main.py --sequence "3031" --models "gpt-4o,claude-3-sonnet" --prompt "Your text here"

# Chain mode with aggregation
uv run python src/main.py --sequence "3100:a-c" --chain-mode --aggregator "3022" --prompt "Complex analysis task"

# Using embedded sequence and model specifications
uv run python src/main.py --prompt "[MODEL:gpt-4o|claude-3-haiku] [SEQ:3031|3100:a-c] Transform this text"
```

## Configuration

Set environment variables for API access:
- `OPENAI_API_KEY`
- `ANTHROPIC_API_KEY`
- `OPENROUTER_API_KEY`

---

## 🎯 **Universal Template-Based Instruction Processing**

A revolutionary system that transforms any instruction or prompt into a standardized, machine-parsable, three-part canonical structure that can be executed in sequences and chained together.

---

# AI Instruction Enhancement System - Complete Documentation

## System Classification & Architecture

**Primary Type**: Template-Driven Instruction Sequence Execution Engine
**Secondary Type**: Multi-LLM Orchestration System
**Operational Pattern**: Sequential Template-Based Transformation Engine

This is a **Meta-Instruction Processing Framework** that transforms inputs through systematic, template-driven processing using multiple LLM providers.

## Core Operational Pathway

```
Template Definition → Template Processing → Catalog Generation → Sequence Resolution → LLM Execution → Chain Processing → Result Aggregation
```

## Template System

### Template Structure
Templates are Python files in `src/templates/` following the pattern:
`{id}-{classification}--{version}.{instruction_name}.py`

**Template Components:**
- `title`: Brief description of the template's purpose
- `interpretation`: Meta-instruction defining the transformation goal
- `transformation`: Structured procedural specification using role-based syntax
- `context`: Optional JSON data for domain-specific processing

**Example Template:**
```python
TEMPLATES = {
    "1000-a-instruction_converter": {
        "title": "Instruction Converter",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it...",
        "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives()]; output={instruction_format=str}}`",
        "context": {}
    }
}
```

### Series Substep Organization
- **X000-series**: `X000-X099` Reserved for `rephrasers`
- **X100-series**: `X100-X199` Reserved for `expanders`
- **X200-series**: `X200-X299` Reserved for `compressors`
- **X300-series**: `X300-X399` Reserved for `...`
- **X400-series**: `X400-X499` Reserved for `...`
- **X500-series**: `X500-X599` Reserved for `...`
- **X600-series**: `X600-X699` Reserved for `...`
- **X700-series**: `X700-X799` Reserved for `generators`
- **X800-series**: `X800-X899` Reserved for `...`
- **X900-series**: `X900-X999` Reserved for `critics`

### Series Organization
- **0000-series**: Sequences from 0000-0999 is reserved for special instructions (just `a`).
- **1000-series**: Sequences from 1000-1999 is reserved for single-step instructions (just `a`).
- **2000-series**: Sequences from 2000-2999 is reserved for double-step instructions (`a-b`).
- **3000-series**: Sequences from 3000-3999 is reserved for `a-c`.
- **4000-series**: Sequences from 4000-4999 is reserved for `a-d`.
- **5000-series**: Sequences from 5000-5999 is reserved for `a-e`.
- **6000-series**: Sequences from 6000-6999 is reserved for `a-f`.
- **7000-series**: Sequences from 7000-7999 is reserved for `a-g`.
- **8000-series**: Sequences from 8000-8999 is reserved for `a-h`.
- **9000-series**: Sequences from 9000-9999 is reserved for `a-i`.

### Template Lifecycle
1. **Python Definition** → Execute template file to generate markdown
2. **Markdown Generation** → Individual `.md` files in `src/generated/`
3. **Catalog Compilation** → JSON catalog (`lvl1.md.templates.json`)
4. **Runtime Execution** → Templates loaded and executed via LLM

## Meta-Instruction Pattern

**Core Principle**: Templates transform instructions rather than execute them directly.

**Standard Pattern**: *"Your goal is not to **answer** the input prompt, but to **[transform]** it"*

**Template Types:**
- **Converters**: Transform input format/structure
- **Critics**: Evaluate and identify improvements
- **Enhancers**: Apply specific enhancement operations

## Sequence System

### Sequence Specification Syntax
- **Basic**: `1000` (single template)
- **Multi-step**: `1000|1001|1900` (sequential execution)
- **Range**: `3100:a-c` (steps a through c)
- **Combined**: `3031|3100:a-c` (multiple sequences)
- **Keyword**: `keyword:distill` (templates containing "distill")
- **Wildcard**: `3100:*enhance*` (steps containing "enhance")

### Chain Mode Processing
Sequential execution where each step's output feeds into the next instruction, creating compound enhancement effects.

### Self-Improving Loops
Sequences like `1000|1001|1900|1000|1001|1900` create recursive enhancement through converter-critic cycles.

## Execution Engine

### Core Components
- **ExecutorConfig**: Centralized configuration with Pydantic validation
- **SequenceManager**: Advanced sequence resolution and filtering
- **TemplateCatalog**: Multi-level template management
- **LiteLLM Integration**: Multi-provider abstraction

### Execution Flow
```python
async def execute_sequence(config: ExecutorConfig) -> List[InstructionResult]:
    for step_id, template_data in sequence_steps:
        current_input = user_prompt if not chain_mode else previous_output
        system_instruction = extract_system_instruction(template_data)
        response = await execute_model_step(model, system_instruction, current_input)
```

## Interface Systems

### Command Line Interface
```bash
# Basic usage
uv run python src/main.py --prompt "Your text here"

# Specific sequence and models
uv run python src/main.py --sequence "3031" --models "gpt-4o,claude-3-sonnet" --prompt "Text"

# Chain mode with aggregation
uv run python src/main.py --sequence "3100:a-c" --chain-mode --aggregator "3022" --prompt "Text"

# Embedded specifications
uv run python src/main.py --prompt "[MODEL:gpt-4o] [SEQ:3031] Transform this text"
```

### Interactive CLI (`src/interactive.py`)
- Sequence browsing with Rich table display
- Interactive model selection
- Execution history with quick re-run
- Enhanced user experience with syntax highlighting

## Data Flow & Storage

### Template Processing (`src/processor.py`)
- **BaseGenerator**: Auto-ID assignment within series ranges
- **Metadata Extraction**: Regex-based parsing with validation
- **Catalog Generation**: JSON compilation with sequence mapping
- **Series Distribution**: Automatic organization by ID ranges

### Output Management
- **Execution History**: JSON files in `src/output/` with complete traces
- **Result Structure**: Structured JSON with metadata, costs, and responses
- **Streaming Support**: Real-time output display during execution

### Context Preservation
- **Initial Prompt**: Carried through entire sequence for consistency
- **Step Outputs**: Accumulated for aggregation and analysis
- **Chain Processing**: Previous outputs feed into subsequent steps

## Advanced Features

### Aggregation System
- **Multi-Step Synthesis**: Combine outputs from multiple sequence steps
- **Result Analysis**: Structured analysis across different enhancement approaches
- **Cost Tracking**: Integrated API usage monitoring across providers

### Template Validation
- **Structure Validation**: Ensures required components (title, interpretation, transformation)
- **Syntax Checking**: Validates transformation syntax and role specifications
- **Context Validation**: JSON schema validation for context data

### Rich Display System (`src/display.py`)
- **Syntax Highlighting**: JSON and code display with Rich console
- **Progress Visualization**: Step-by-step execution tracking
- **Interactive Elements**: Clickable sequences and model selection

## Extensibility Points

### Template System Extensions
- **New Series**: Add specialized domains in 4000-9000 ranges
- **Custom Processors**: Extend BaseGenerator for new template types
- **Context Integration**: Domain-specific JSON context processing

### Execution Engine Extensions
- **New Providers**: Add LLM providers via LiteLLM integration
- **Custom Aggregators**: Specialized result synthesis logic
- **Streaming Enhancements**: Real-time processing improvements

### Sequence Management Extensions
- **Advanced Filtering**: New selection patterns and operators
- **Dynamic Sequences**: Runtime sequence generation based on content
- **Conditional Logic**: Template selection based on analysis results

## Key Architectural Strengths

1. **Universal Applicability**: Works on any input while maintaining consistent patterns
2. **Composable Enhancement**: Individual templates combine into complex workflows
3. **Self-Referential Improvement**: System can enhance its own instructions
4. **Modular Extensibility**: New patterns added without core logic changes
5. **Multi-Provider Flexibility**: Optimal model selection per enhancement step
6. **Rich Interaction Model**: Both CLI and programmatic interfaces

## Usage Patterns

### Basic Enhancement
Single template application for simple transformations.

### Sequential Processing
Multi-step enhancement through template chains.

### Iterative Improvement
Converter-critic loops for progressive refinement.

### Comparative Analysis
Multiple models on same sequence for result comparison.

### Domain Specialization
Context-driven templates for specific domains or use cases.

This system creates a **universal instruction enhancement engine** that consistently transforms any input through systematic, template-driven processing while maintaining flexibility for diverse domains and enhancement requirements.
