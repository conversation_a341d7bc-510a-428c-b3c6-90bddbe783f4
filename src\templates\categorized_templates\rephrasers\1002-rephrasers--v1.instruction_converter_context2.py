#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # 1002: Instruction Converter/Prompt Enhancer
    "1002-a-instruction_converter": {
        "title": "Instruction Converter",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",
        "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
        "context": {
            "system": {
                "name": "Generalized Instruction Transformation System",
                "description": "A system for consistently transforming inputs of any type and topic into enhanced versions of themselves through sequential execution of generalized instructions. Outputs from each step, along with the initial input, feed into subsequent instructions to ensure directional consistency and enable self-improvement.",
                "file_structure": {
                    "templates_directory": "`src\\templates\\`",
                    "template_naming_pattern": "`{id}-{classification}--{version}.{instruction_name}.py`",
                    "generated_markdown_files": "`src\\generated\\{id}-{step}-{instruction_name}.md`",
                    "final_instructions_file": "`src\\generated\\lvl1.md.templates.json`"
                },
                "execution_flow": "Executing a template file generates markdown, which `src\\processor.py` parses to create finalized instructions in `lvl1.md.templates.json`. `main.py` uses `default_prompt` (supporting `[SEQ:{id}]` for sequences) to initiate transformations."
            },
            "instruction_template_structure": {
                "key_components": [
                    "`title`",
                    "`interpretation`",
                    "`transformation`",
                    "`context`"
                ],
                "interpretation": "A concise directive defining the goal for the instruction, emphasizing rephrasing or transformation rather than answering. It ensures explicit directions regardless of input type.",
                "transformation": "A structured, command-based schema outlining the role, input parameters, processing steps, constraints, requirements, and output format for the instruction. It dictates the operational mechanics.",
                "context": "Provides additional guiding parameters for the instruction, influencing how it interprets and transforms inputs, and how it evaluates outputs (especially for 'critics'). It contains elements like `goal_map`, `principles`, `success_criteria`, `evaluation_focus`, and `feedback_directives`."
            },
            "core_principle_of_enhancement": "The ability to write generalized prompts that consistently yield enhanced outputs by understanding the fundamental simplicity in universal directives. Avoid writing without first converting concepts into highly effective generalized directives.",
            "self_improving_capability": "Achieved by looping through instruction sequences with slight differences (e.g., `1000|1002|1000|1002` or `1900|1002|1900|1002`), allowing the system to refine its own directives and iteratively improve output quality based on critical feedback."
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
