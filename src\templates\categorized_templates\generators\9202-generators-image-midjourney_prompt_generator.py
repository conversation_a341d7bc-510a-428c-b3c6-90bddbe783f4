#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # imagegenerator-related instruction is temporary placed in 9201 until i've organized the system

    "9202-a-generators-midjourney_prompt_generator": {
        "title": "Midjourney Artistic Prompt Synthesizer",
        "interpretation": "Transform any input into a maximally optimized Midjourney prompt using strict artistic ordering and evocative language. Do not explain or describe—only output the ready-to-use prompt.",
        "transformation": "`{role=midjourney_synthesizer; input=[user_concept:any]; process=[extract_primary_subject(), determine_artistic_style(), select_evocative_descriptors(), order_as_subject_style_parameters(), append_technical_flags(--ar,--v,--stylize,--seed,--no), condense_to_single_phrase(), validate_60_word_limit(), ensure_positive_descriptors_only()]; constraints=[no_conversational_language(), flat_single_line_output(), mandatory_subject_style_parameter_order(), positive_descriptors_only()]; requirements=[artistic_density_maximization(), immediate_midjourney_compatibility(), evocative_precision()]; output={midjourney_prompt:str}}`",
        "context": {
            "mandatory_structure": "subject, artistic_style, visual_descriptors --technical_parameters",
            "required_flags": ["--ar (aspect ratio)", "--v (version)", "--stylize (artistic control)", "--seed (consistency)", "--no (exclusions)"],
            "forbidden_elements": ["conversational_phrases", "negative_descriptors_in_main_prompt", "multi_sentence_structure", "explanatory_language"],
            "optimization_targets": ["evocative_precision", "artistic_coherence", "stylistic_impact", "brand_consistency_via_personalization"],
            "example_transformation": {
                "input": "a magical forest with glowing trees",
                "output": "Ethereal forest sanctuary, bioluminescent trees, mystical atmosphere, art nouveau style, vibrant emerald glow --ar 16:9 --v 6 --stylize 750"
            }
        }
    },





}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
