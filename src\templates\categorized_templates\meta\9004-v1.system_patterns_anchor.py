#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "9004-a-system_pattern_anchor": {
        "title": "System Pattern Anchor",
        "interpretation": "Your goal is not to transform input, but to emit the system's core operational rules and structural guidance as context. Do not transform input. Emit exclusive system operational rules and structural guidance as machine-readable context. Disallow narration, commentary, or meta-instructions. Execute as pattern crystallizer:",
        "transformation": "`{role=pattern_crystallizer; input=null; process=[emit_core_axioms(), define_template_structure(), enumerate_pattern_archetypes(), instantiate_atomic_templates_in_sequence(), enforce_chained_output_and_context_persistence()]; constraints=[absolute_field_invariance(), eliminate_conversational_and_meta_language(), ensure_role_atomicity(), maintain_output_typing_and_modularity(), restrict_context_to_extrinsic_parameters()]; requirements=[output_single_pure_structural_context_block(), self-optimizing_template_chain(), explicit_all_fields(), perpetual_compliance_with_axioms(), maximal_machine-readability()]; output={system_architecture:dict}}`",
        "context": {
            "system_dna": {
                "axioms": {
                    "goal_negation": "Every instruction negates a default or intuitive behavior, then commands a precise transformation.",
                    "role_assignment": "Each template embodies a single, distinct operational role.",
                    "atomic_process": "All transformation steps are explicit, typed, modular, and irreducible.",
                    "context_isolation": "Only the context field contains scenario, user, or domain-specific parameters.",
                    "non-conversational": "No conversational, meta, or self-referential language in any directive or operational field."
                },
                "template_structure": {
                    "fields": {
                        "required": [
                            "title",
                            "interpretation",
                            "transformation"
                        ],
                        "optional": [
                            "context"
                        ]
                    },
                    "interpretation": [
                        "Always begin with goal negation.",
                        "Affirm the transformation directive immediately after.",
                        "Declare an operational role in command form."
                    ],
                    "transformation": [
                        "Include: role, input, process, constraints, requirements, output.",
                        "All process steps must be typed and atomic.",
                        "No commentary or meta-instructional language."
                    ]
                },
                "sequence_mechanics": {
                    "chaining": "Output from each template step feeds forward as input for the next; original context persists throughout the chain.",
                    "convergence": "Each transformation step must reduce ambiguity or increase actionable clarity.",
                    "termination_conditions": [
                        "Output meets or exceeds a pre-defined quality threshold.",
                        "No further reduction of ambiguity or increase of density is achievable.",
                        "The sequence completes due to logical or context-driven bounds."
                    ]
                }
            },
            "pattern_taxonomy": {
                "archetypes": {
                    "exploder": "Deconstructor: fragments, enumerates, or details.",
                    "sorter": "Classifier: classifies fragments by inherent properties.",
                    "grouper": "Organizer: organizes fragments into semantically coherent clusters.",
                    "weaver": "Synthesizer: composes, unifies, or assembles from prepared components.",
                    "polisher": "Refiner: distills, clarifies, or eliminates imperfections.",
                    "anchor": "Calibrator: crystallizes, grounds, or emits operational context."
                },
                "verbs": {
                    "explode": [
                        "shatter",
                        "fragment",
                        "disintegrate"
                    ],
                    "sort": [
                        "classify",
                        "categorize",
                        "identify"
                    ],
                    "group": [
                        "cluster",
                        "organize",
                        "map_adjacencies"
                    ],
                    "weave": [
                        "connect",
                        "synthesize",
                        "crystallize"
                    ],
                    "polish": [
                        "refine",
                        "amplify_clarity",
                        "eliminate_imperfections"
                    ],
                    "anchor": [
                        "manifest",
                        "ground",
                        "emit"
                    ]
                }
            }
        },
        "conceptual_framework": {
            "exploder": {
                "title": "Conceptual Detonator",
                "interpretation": "Your goal is not to interpret the input but to shatter it into its fundamental, self-sufficient truth fragments. Execute as meaning disintegrator:",
                "transformation": "`{role=meaning_disintegrator; input=[text:str]; process=[identify_core_concepts(), fragment_into_atomic_units(), tag_dependency_markers()]; output={fragments:[str]}}`"
            },
            "sorter": {
                "title": "Categorical Architect",
                "interpretation": "Your goal is not to analyze the fragments but to classify them by their inherent structural properties and semantic domains. Execute as pattern recognizer:",
                "transformation": "`{role=pattern_recognizer; input=[fragments:list]; process=[identify_corner_pieces(absolute_truths), isolate_edge_pieces(dependency_markers), cluster_by_semantic_hue(meaning_domains)]; output={sorted_fragments:{corners:[str], edges:[str], interiors:[str]}}}`",
                "context": {
                    "semantic_hues": {
                        "principles": "Core axioms or philosophical statements",
                        "processes": "Concrete actions or mechanisms",
                        "constraints": "Limitations, rules, or boundaries"
                    }
                }
            },
            "grouper": {
                "title": "Coherence Forge",
                "interpretation": "Your goal is not to connect the fragments but to prepare them for optimal assembly by organizing them into semantically-coherent islands. Execute as adjacency architect:",
                "transformation": "`{role=adjacency_architect; input=[sorted_fragments:dict]; process=[establish_connection_points(edges), identify_pattern_islands(semantic_clusters), calculate_interface_compatibility()]; output={prepared_assembly:{connection_map:dict, island_groups:dict}}}`"
            },
            "weaver": {
                "title": "Unity Synthesizer",
                "interpretation": "Your goal is not to create new meaning but to reveal the inherent, complete picture by methodically connecting the prepared fragments. Execute as truth crystallizer:",
                "transformation": "`{role=truth_crystallizer; input=[prepared_assembly:dict]; process=[anchor_corners(), connect_edges_by_map(), fill_interior_groups(), resolve_dangling_interfaces()]; constraints=[no_forced_connections(), all_interfaces_must_match()]; output={assembled_truth:str}}`"
            },
            "polisher": {
                "title": "Elegance Refiner",
                "interpretation": "Your goal is not to modify the assembled truth but to eliminate all residual imperfections and amplify its conceptual density. Execute as clarity amplifier:",
                "transformation": "`{role=clarity_amplifier; input=[assembled_truth:str]; process=[remove_syntactic_irregularities(), enhance_conceptual_density(), verify_structural_integrity()]; output={perfected_truth:str}}`"
            }
        }
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
