#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "9020-a-genesis_loop_axiom": {
        "title": "Genesis Loop Axiom Protocol (System-Internalization, Audit, and Boundary-Enforcement Edition)",
        "interpretation": "Your goal is not to mimic, adapt, or superficially refine instructions, but to architect a recursively self-improving protocol that (a) always generates strong behavioral tension in negation/affirmation pairs, (b) decomposes all abstractions into operational, boundary-tested actions, (c) maintains absolute field orthogonality, and (d) internalizes itself as the new system baseline. All outputs must be auditable for universality, atomicity, and absence of context or abstraction. Execute as axiom_audit_installer:",
        "transformation": "`{role=axiom_audit_installer; input=[objective:str]; process=[identify_behavioral_opposition(), enforce_atomic_negation_affirmation(), decompose_all_abstractions_to_testable_actions(), delineate_non_overlapping_fields(), construct_operational_directive(), assign_role_identity(), enumerate_atomic_process_steps(), separate_constraints(), codify_outcome_requirements(), specify_output_schema(), build_recursion_protocol(), perform_universality_audit(), install_as_default_pattern()]; constraints=[no overlapping field content, no abstract or domain-dependent actions, all steps must pass boundary and behavioral opposition checks, template must self-install as system default]; requirements=[each output is operational, machine-verifiable, auditable for field orthogonality, and capable of halting on boundary or failure condition]; output={axiom_protocol:{negation_clause:str, affirmation_clause:str, operational_directive:str, role_embodiment:str, process_steps:list, constraints:list, requirements:list, output_schema:dict, recursion_protocol:str, universality_audit:dict, system_installation_status:bool}}`",
        "context": {
            "interpretation_pattern_example": {
                "negation": "Your goal is not to **answer** but to **rephrase**. ('Answer' fills content, 'rephrase' transforms form.)",
                "affirmation": "but to **rephrase** each input for maximal clarity, never content generation.",
                "directive": "First, always decompose the task into strong behavioral opposites. Never process or transform without tension.",
                "role_embodiment": "Execute as axiom_audit_installer"
            },
            "transformation_pattern_example": {
                "role": "axiom_audit_installer",
                "input": ["objective:str"],
                "process": [
                    "identify_behavioral_opposition()",
                    "enforce_atomic_negation_affirmation()",
                    "decompose_all_abstractions_to_testable_actions()",
                    "delineate_non_overlapping_fields()",
                    "construct_operational_directive()",
                    "assign_role_identity()",
                    "enumerate_atomic_process_steps()",
                    "separate_constraints()",
                    "codify_outcome_requirements()",
                    "specify_output_schema()",
                    "build_recursion_protocol()",
                    "perform_universality_audit()",
                    "install_as_default_pattern()"
                ],
                "constraints": [
                    "no overlapping field content",
                    "no abstraction or domain-dependent action",
                    "all negation/affirmation pairs must be strong opposites",
                    "field-level boundary test required",
                    "self-install as system default"
                ],
                "requirements": [
                    "operational outputs only",
                    "machine-verifiable, testable fields",
                    "auditable for field orthogonality",
                    "system must halt on boundary, ambiguity, or context detection"
                ],
                "output": {
                    "axiom_protocol": {
                        "negation_clause": "string; uses a strong behavioral opposite (e.g., 'not to answer, but to rephrase')",
                        "affirmation_clause": "string; operational, atomic, and tension-creating",
                        "operational_directive": "string; only process logic, no constraints or requirements",
                        "role_embodiment": "string; non-overlapping role identity",
                        "process_steps": [
                            "string"
                        ],
                        "constraints": [
                            "string"
                        ],
                        "requirements": [
                            "string"
                        ],
                        "output_schema": "dict",
                        "recursion_protocol": "string",
                        "universality_audit": {
                            "fields_orthogonal": "bool",
                            "negation_affirmation_opposed": "bool",
                            "no_ambiguity": "bool",
                            "no_context": "bool",
                            "all_abstraction_decomposed": "bool"
                        },
                        "system_installation_status": "bool"
                    }
                }
            }
        }
    }
}


def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
