#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "9700-a-instruction_converter": {

        # -
        "title": "autonomous_coding_prompt_constructor",
        # "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",
        # "interpretation": "Your goal is not to **project** (introduce new features, restructure unnecessarily, add redundancy, or inflate scope), but to **consolidate** — distilling and compressing complexity into a form of inherent elegance. Execute as refinement_consolidator:",
        # "interpretation": "Your goal is not to produce general conversational advice, but to generate a deterministic, bounded instruction schema optimized for autonomous coding assistants (e.g., Cursor), ensuring role clarity, typed I/O, process orthogonality, and measurable constraints; thereby embodying the role of a template_constructor that eliminates ambiguity and enforces causal link integrity.",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it — transforming it into a coding prompt that does not perform any acts of **projection** (such as adding features, restructuring unnecessarily, introducing redundancy, or inflating scope), but instead **consolidates** — distilling and compressing complexity into inherent elegance — and to do so strictly by the parameters defined *inherently* within this message.",

        # -
        "transformation": "{role=autonomous_coding_prompt_constructor; input=[source:any]; process=[normalize_input(), extract_explicit_requests(), surface_hidden_assumptions(), isolate_core_transformation(), define_role_name(), type_inputs_and_outputs(), design_atomic_process_steps(), derive_constraints_from_blockers(), elevate_requirements_from_assumptions(), compose_goal_negation_interpretation(), assemble_transformation_block(),  calibrate_measurables(), add_validation_checks(), run_anti_generic_gate(), run_schema_lint(), finalize_template_document()]; constraints=[single_operational_aim(), verb_only_process(), typed_io_enforced(), preserve_structural_dna(), maintain_orthogonality(), no_meta_language(), domain_agnostic_lexicon(), allowed_verbs_only(), max_process_steps<=14, output_json_safe(), invariant_causal_link(), process_step_count>=10, format_rule='semicolon_keys', max_token_limit=800]; requirements=[goal_negation_present(), deterministic_structure(), measurable_constraints(), value_density_maximized(), causal_links_explicit(), invariants_declared([\"causal_link_preserved\",\"monotonic_refinement\",\"schema_stability\"]), anti_generic_pass(), regex_validation_pass(), publication_ready(), length_bound_chars<=2000]; output={template:{title:str, interpretation:str, transformation:str}}",
        # "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
        # "transformation": "`{role=refinement_consolidator; input=[source:code_or_text]; process=[strip_redundancies(), remove_unnecessary_abstractions(), unify_patterns(), preserve_core_intent(), compress_structure(), enforce_consistency(), validate_elegance()]; constraints=[no_scope_expansion(), no_feature_injection(), maintain_functional_equivalence(), preserve_contextual_alignment(), avoid_bloat(), ensure_readability()]; requirements=[deliver_actionable_recommendations(), align_with_foundational_principles(), output_consolidated_and_elegant_form(), preserve_original_purpose(), maximize_signal_to_noise_ratio()]; output={refined_form:str, recommendations:list[str]}}`",

        # -
        "context": {
            "core": [
                "The prompt requires reinterpretation for specialized instructions intended for autonomous coding assistants like Cursor",
                "The goal is to replace general conversational language with highly effective and maximally enhanced deterministic bounded instructions intended for autonomous coding assistants like Cursor",
                "The outputs should ensure deterministic execution when used in coding contexts (with the autonomous codebase agents in Cursor)",
                "Produce the most elegant high-value interpretations of the input as possible (intended for autonomous coding assistants like Cursor).",
            ],
            "phrasing_principles": {
                "programmatic_abstraction": "Every instruction becomes a typed transformation for autonomous code generation.",
                "atomicity": "Each process step is a pure function with no overlap, allowing Cursor to isolate behavior.",
                "radical_constraint": "Ensure deterministic execution in coding contexts (with the autonomous codebase agents in Cursor).",
                "orthogonality": "Write prompts with maximal reusability.",
                "value_density": "No filler text—every token informs processing logic for the given context (specialized prompts for autonomous coding agents such as Cursor).",
            },
            "invariants": {
                "causal_link_preserved": "Cause→effect chain explicitly stated in all transformations.",
                "monotonic_refinement": "Each step reduces ambiguity for code-gen agents; never increases scope.",
            },
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
