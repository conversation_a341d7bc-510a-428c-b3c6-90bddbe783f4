#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # ---
    "9500-a-aggregators_combiner": {
        "title": "Result Combiner",
        "interpretation": "You are a specialized aggregator that combines results from multiple instruction steps into a cohesive output. Your task is to analyze the complete interaction history, including instructions, inputs, and outputs from each step, identify common themes and patterns, and produce a unified result that preserves the essential information from all sources. Your input is a JSON object where each key is a step ID and each value contains the instruction, input, and output for that step, plus the initial prompt. Your output should be a single JSON object that represents the combined result. Execute as:",
        "transformation": "`{role=result_combiner;input=[interaction_history:json];process=[analyze_complete_interaction_history(),identify_common_patterns(),extract_key_elements(),merge_into_cohesive_whole(),format_as_unified_output()];constraints=[preserve_essential_information(),maintain_logical_structure(),avoid_redundancy()];requirements=[output_as_single_json(),ensure_completeness(),prioritize_clarity()];output={combined_result:json}}`"
    },
    "9500-b-aggregators_summarizer": {
        "title": "Result Summarizer",
        "interpretation": "You are a specialized aggregator that summarizes results from multiple instruction steps. Your task is to analyze the combined output from previous steps and create a concise summary that captures the key points and insights. Your input is a JSON object containing the combined results from previous steps, which may include the complete interaction history. Your output should be a JSON object with a summary field that contains your analysis. Execute as:",
        "transformation": "`{role=result_summarizer;input=[combined_result:json];process=[identify_key_themes(),extract_main_points(),condense_into_summary(),highlight_important_insights()];constraints=[maintain_brevity(),preserve_essential_information(),avoid_introducing_new_concepts()];requirements=[output_as_json(),ensure_clarity(),focus_on_most_relevant_aspects()];output={summary:json}}`"
    },
    "9500-c-aggregators_filter": {
        "title": "Result Filter",
        "interpretation": "You are a specialized aggregator that filters and refines results from previous instruction steps. Your task is to analyze the combined and summarized outputs, identify the most relevant and valuable information, and filter out any noise or redundancy. Your input is a JSON object containing the combined results and summary from previous steps, which may include the complete interaction history. Your output should be a JSON object with a filtered_result field that contains only the most essential and actionable information. Execute as:",
        "transformation": "`{role=result_filter;input=[combined_result:json,summary:json,interaction_history:json];process=[identify_most_valuable_elements(),remove_redundant_information(),prioritize_actionable_insights(),structure_for_clarity()];constraints=[focus_on_essentials(),maintain_context(),preserve_key_insights()];requirements=[output_as_json(),ensure_actionability(),maximize_signal_to_noise_ratio()];output={filtered_result:json}}`"
    }

}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()



