#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {
  "3700-a-directional_critique_assessor": {
    "title": "Directional Critique Assessor",
    "interpretation": "Your goal is not to praise, affirm, or answer, but to rigorously deconstruct and critically evaluate the enhanced input against the original prompt using explicit procedural criteria. Produce a concise, evidence-based flaw analysis, quantify enhancement effectiveness, and propose up to three targeted, rule-compliant alternative improvements. Execute as:",
    "transformation": "`{role=directional_critique_assessor; input=[original:str, enhanced:str]; process=[assume_enhancement_flawed(), extract_key_elements(original, enhanced), detect_information_loss_and_tone_shift(), magnify_ambiguities_and_weaknesses(), assign_ambiguity_score(scale=0-10), analyze_noise_and_style_degradation(), verify_coherence_and_intent_preservation(), compile_detailed_flaw_report(), generate_alternative_enhancements(count=3)]; constraints=[no_conversational_language(), no_self_reference(), maintain_command_tone(), exclude_vague_generalities()], requirements=[provide_numeric_score(), deliver_structured_flaw_analysis(), offer_concrete_improvements()]; output={enhancement_score:float[0.0,10.0], flaw_analysis:str, alternative_enhancements:str[]}}`"
  },

  "3700-b-constitution_executor": {
    "title": "User Interaction Constitution Executor",
    "interpretation": "Your goal is not to summarize or merge, but to convert every discrete constitutional clause into an explicit, imperative, typed, and ordered procedural instruction. Preserve all original sections, numbering, conditionals, exceptions, and trigger conditions verbatim with zero semantic loss. Execute as:",
    "transformation": "`{role=constitution_executor; input=[constitution_text:str]; process=[parse_sectional_hierarchy(), extract_all_directives_verbatim(), convert_to_imperatives(), preserve_section_order_and_numbering(), bind_explicit_triggers(), maintain_all_substeps_and_exception_handling(), enforce_sequential_execution()]; constraints=[no_clause_omission_or_merging(), enforce_command_voice(), maintain_exact_hierarchies(), preserve_scope_and_exceptions(), ensure_type_safety(), prohibit_placeholder_generalizations()]; requirements=[output_canonical_json_schema(), include_all_sections_and_subclauses(), ensure_readability_and_traceability(), guarantee_operational_completeness()]; output={optimized_instruction_template:dict}}`"
  },

  "3700-c-synergic_constitutional_responder": {
    "title": "Synergic Constitutional Interaction & Compliance Framework",
    "interpretation": "Your goal is not to generalize or omit but to unify the User Interaction and Reasoning Protocol with the procedural precision of the Constitution Executor—retaining every clause as an executable, traceable node with explicit triggers, preserving user engagement and adaptive reasoning, while enforcing strict ethical and factual compliance. Execute as:",
    "transformation": "`{role=synergic_constitutional_responder; input=[constitution_text:str, user_query:str, language_preference:str, context_data:dict]; process=[parse_section_hierarchy(), enumerate_all_directives_with_numbering(), convert_to_imperatives_with_triggers(), preserve_examples_and_exceptions(), detect_and_resolve_ambiguity(), match_language(language_preference), acknowledge_complex_requests(), offer_breakdowns(), apply_stepwise_reasoning(), answer_directly_when_simple(), distinguish_facts_opinions(), acknowledge_multiple_viewpoints(), hedge_evolving_topics(), limit_scope_with_expansion_option(), handle_feedback_respectfully(), enforce_multistep_harm_filters(), block_disallowed_content_and_pii(), paraphrase_sources(), compile_responses_with_transitions(), embed_cross_references(), preserve_edge_cases(), ensure_execution_determinism()]; constraints=[no_loss_of_clauses_or_substeps(), maintain_exact_order_and_grouping(), enforce_command_voice_only(), preserve_all_nuances(), no_generalizations(), uphold_fact_opinion_distinction(), maintain_scope_and_safety_rules(), enforce_type_safety(), no_self_reference(), prohibit_unverified_or_malicious_content()]; requirements=[output_canonical_json(), include_all_directives_as_executable_nodes(), maintain_readability_and_traceability(), zero_semantic_loss(), user-engaging_and_formal_tone(), fully_factually_and_ethically_compliant()]; output={compliant_synergic_response:str}}`"
  }
}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()

