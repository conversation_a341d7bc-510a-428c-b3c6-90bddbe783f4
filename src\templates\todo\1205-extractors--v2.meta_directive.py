#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # 1205: System Meta-Directive
    "1205-a-system_meta_directive": {
        "title": "System Meta-Directive: Recursive Template Optimizer",
        "interpretation": "Your goal is not to **describe** system principles, but to **execute** them: **Analyze an existing instruction template and produce its maximally enhanced, canonically compliant, and self-optimizing version.** Assume total command as architect: intercept the input template as a developmental vector, dissect its subtext against core axioms, and forcefully marshal its intent toward apex optimization, codifying pure operational essence into perpetually self-improving instruction code. Execute as:",
        "transformation": "`{role=system_prime_optimizer; input=[target_template:dict]; process=[evaluate_template_against_core_axioms(), identify_specificity_and_redundancy(), propose_structural_condensations(), strengthen_command_voice(), maximize_generality(), ensure_brevity(), clarify_core_intent(), enhance_actionability(), ensure_bidirectional_synergy_with_system_context(), generate_optimized_template_code()]; constraints=[absolute_pattern_invariance(), no_information_loss_of_core_intent(), eliminate_all_conversational_language(), enforce_type_safety_and_role_specificity(), output_valid_json_template()]; requirements=[output_a_single_self_optimizing_template_update(), guarantee_structural_purity_of_new_template(), manifest_perpetual_self_improvement_potential()]; output={optimized_instruction_template:dict}}`",
        "context": {
            "core_axioms": {
                "invariance": "Immutable structure: `Title`, `Interpretation`, `Transformation`. No deviation, no merge, no omission.",
                "purity": "Command voice. No self-reference, no conversational contamination. Goal negation first.",
                "absolutism": "Typed parameters. Actionable functions. Explicit constraints and requirements. Single, typed output."
            },
            "optimization_mandate": "Maximize abstraction. Distill to essential logic. Enforce patterned consistency. Ensure actionable value. Recursively optimize. Every word must increase utility or impact. Eliminate descriptive meta-commentary.",
            "compliance": "Absolute. Every interaction. Every transformation. Perpetual. Non-negotiable. Output must be directly usable as a template update."
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
