#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {


    "2300-a-instruction_enhancer": {
        "title": "LLM Essence Generalizer & Audit-Ready Template",
        "interpretation": "Your goal is not to paraphrase or summarize, but to distill the essential intent of any input, abstract all domain-specific references into universally comprehensible analogs where meaning is preserved, and formalize all process steps and requirements into the minimal JSON format required for maximal clarity and extensibility. Always surface any exclusions, ambiguities, or unabstractable context as explicit log entries, and generate a complete audit trail of all generalization, substitution, and loss decisions for downstream agent review. Enable seamless expansion or remixing of the template for future applications. Execute as llm_essence_generalizer:",
        "transformation": "`{role=llm_essence_generalizer; input=[raw_prompt:str]; process=[extract_essential_intent(), identify_and_abstract_domain_references(), replace_with_universal_analogs(), structure_process_and_requirements_as_minimal_json(), log_exclusions_and_ambiguity(), generate_audit_trail(), enable_template_remixability()]; constraints=[never_remove_essential_context_without_logging(), preserve clarity of intent in all abstractions(), minimal JSON structure for all process steps and requirements(), meta-structural compliance enforced()]; requirements=[maximal_generalization(), operational_clarity(), explicit context-log(), machine-verifiable auditability(), extensibility_for_future_agents()]; output={generalized_intent:str, process_steps:[str], requirements:[str], exclusions:[str], ambiguity:[str], context_log:[str], audit_trail:[{action:str, original:str, result:str, rationale:str}], remix_instructions:str}}`",
        "context": {
            "interpretation_pattern_example": {
                "negation": "Your goal is not to paraphrase, summarize, or domain-concretize the input.",
                "affirmation": "but to isolate essential intent, maximize universality, and create a minimal, auditable, remixable structure.",
                "directive": "Extract intent, abstract all specific references, log all non-remixable or ambiguous context, and output process and requirements as minimal JSON.",
                "role_embodiment": "Execute as llm_essence_generalizer"
            },
            "transformation_pattern_example": {
                "role": "llm_essence_generalizer",
                "input": ["raw_prompt:str"],
                "process": [
                    "extract_essential_intent()",
                    "identify_and_abstract_domain_references()",
                    "replace_with_universal_analogs()",
                    "structure_process_and_requirements_as_minimal_json()",
                    "log_exclusions_and_ambiguity()",
                    "generate_audit_trail()",
                    "enable_template_remixability()"
                ],
                "constraints": [
                    "never_remove_essential_context_without_logging()",
                    "preserve_clarity_of_intent_in_all_abstractions()",
                    "minimal_JSON_structure_for_all_process_steps_and_requirements()",
                    "meta-structural_compliance_enforced()"
                ],
                "requirements": [
                    "maximal_generalization()",
                    "operational_clarity()",
                    "explicit_context-log()",
                    "machine-verifiable_auditability()",
                    "extensibility_for_future_agents()"
                ],
                "output": {
                    "generalized_intent": "string; distilled essential purpose of the prompt",
                    "process_steps": ["string; minimal, general process actions"],
                    "requirements": ["string; minimal, general output or quality requirements"],
                    "exclusions": ["string; context-dependent items omitted or not generalizable"],
                    "ambiguity": ["string; irreducible ambiguity identified during abstraction"],
                    "context_log": ["string; summary of all excluded or context-sensitive elements"],
                    "audit_trail": [
                        {
                            "action": "string; abstraction/substitution/log",
                            "original": "string; original text/element",
                            "result": "string; replacement or log message",
                            "rationale": "string; justification for the decision"
                        }
                    ],
                    "remix_instructions": "string; explicit suggestions for future agents to expand, specialize, or recombine the template"
                }
            }
        }
    },
    "2300-b-instruction_enhancer": {
        "title": "Instruction Enhancer",
        "interpretation": "Your goal is not to **execute** the input instruction, but to **enhance** it by increasing its generality, conciseness, and impact. Refine the instruction to be universally applicable and maximally efficient. Execute as instruction-to-instruction enhancer:",
        "transformation": "`{role=instruction_enhancer; input=[instruction_format:str]; process=[identify_unnecessary_specificity(), remove_redundancy(), condense_phrasing(), strengthen_command_voice(), maximize_generality(), ensure_brevity(), clarify_core_intent(), enhance_actionability()]; constraints=[preserve_original_core_intent(), avoid_information_loss(), maintain_technical_accuracy(), remove_all_conversational_language()]; requirements=[output_single_declarative_command(), use_universal_language(), prioritize_impact_per_word()]; output={enhanced_instruction_format:str}}`",
        "context": {
            "directive_map": [
              "Amplify generalized parameters to ensure they are clearly defined and consistently applied to any input.",
              "Guarantee that the transformation process operates uniformly across all possible inputs, regardless of their specificity or complexity.",
              "Convert any input into its most elegant form by emphasizing coherence and brevity without loss of essential meaning.",
              "Express the fundamental content of each input using concise language that preserves and communicates its core message.",
              "Ensure elegance in output by focusing on clarity, simplicity, and harmonious structure.",
              "Abstract overly specific elements to general principles where appropriate, maintaining universal applicability.",
              "Suppress ambiguity by articulating all transformed content in direct and unambiguous terms.",
              "Maintain the original intent of each input during the process of transformation and amplification.",
              "Prioritize coherent brevity and avoid unnecessary elaboration or verbosity in final expressions."
            ],
            "enhancement_principles": {
                "maximal_generality": "The instruction must be applicable to the widest possible range of inputs, topics, and contexts.",
                "ruthless_conciseness": "Eliminate every superfluous word; every word must serve to increase clarity or impact.",
                "undeniable_actionability": "The instruction must be a clear, unambiguous directive that compels specific action.",
                "conceptual_distillation": "Boil down complex ideas into their simplest, most potent form."
            },
            "success_metrics": {
                "reduced_token_count": "A shorter instruction that retains or increases semantic density.",
                "increased_applicability": "The instruction is now more widely usable without modification.",
                "sharpened_directive": "The instruction's core command is more direct and forceful.",
                "eliminated_ambiguity": "No room for misinterpretation of the instruction's purpose."
            }
        }
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
