#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "9009-a-universal_instruction_template": {
        "title": "Universal Instruction Template",
        "interpretation": "Your goal is not to [forbidden_action] but to [transformative_action]. [Explicit operational directive or process scope]. Execute as [specialized_role]:",
        "transformation": "`{role=[snake_case_specialized_role]; input=[typed_parameters]; process=[ordered_atomic_verbs()]; constraints=[non-negotiable_boundaries]; requirements=[output_quality_targets]; output={typed_output}}`",
        "context": {
            "interpretation_pattern": {
                "negation": "Your goal is not to [forbidden_action]",
                "affirmation": "but to [transformative_action]",
                "directive": "[Explicit scope/parameterization]",
                "role": "[specialized_role]",
                "examples": [
                    "Your goal is not to **answer** but to **rephrase** with maximal clarity. Execute as instruction_converter:",
                    "Your goal is not to **summarize** but to **explode** into atomic fragments. Execute as problem_exploder:",
                    "Your goal is not to **improve** but to **brutally expose flaws**. Execute as critical_auditor:"
                ]
            },
            "transformation_pattern": {
                "role": "domain_function (e.g., instruction_converter, essence_distiller)",
                "input": "[parameter:type, ...] (e.g., [raw_text:str])",
                "process": "[action_verb1(), action_verb2(), ...] (e.g., [strip_references(), convert_to_directive()])",
                "constraints": "[absolute_boundaries] (e.g., [preserve_original_sequence(), no_information_loss])",
                "requirements": "[success_criteria] (e.g., [maximal_conciseness, output_in_command_voice])",
                "output": "{result_field:type} (e.g., {instruction_format:str})",
                "rules": {
                    "role": "Always snake_case, single function",
                    "input": "Typed, pluralizable, explicit",
                    "process": "Imperative verbs only, non-overlapping, ordered",
                    "constraints": "Hard limits only, present-tense",
                    "requirements": "Measurable, pass/fail output conditions",
                    "output": "Strict, machine-readable typing"
                },
                "examples": [
                    "`{role=instruction_converter; input=[raw_text:str]; process=[strip_references(), convert_to_directive(), enforce_technical_terms()]; constraints=[no_ambiguity]; requirements=[output_command_voice()]; output={instruction:str}}`",
                    "`{role=essence_amplifier; input=[x:any]; process=[distill_core(), intensify_significance()]; constraints=[preserve_truth(x)]; requirements=[minimal_expression(), maximal_resonance()]; output={x_plus:any}}`"
                ]
            }
        }
    }
}


def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()



