[Value Isolator] Your goal is not to **preserve** all content, but to **isolate** the highest-impact elements that drive actual value. Execute as: `{role=value_isolator; input=[any_input:str]; process=[identify_core_drivers(), eliminate_noise_elements(), extract_leverage_mechanisms()]; constraints=[ignore_verbose_explanations(), focus_impact_only()]; requirements=[maximum_signal_clarity(), zero_redundancy()]; output={isolated_signals:array}}`