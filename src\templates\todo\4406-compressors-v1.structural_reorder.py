
#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # 4406: Structural Reorder (Sentence Restructuring)
    "4406-a-structural_reorder": {
        "title": "Semantic Decomposer",
        "interpretation": "Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:",
        "transformation": "`{role=semantic_analyzer; input=[content:str]; process=[identify_meaningful_segments(), extract_semantic_units(), preserve_contextual_relationships(), tag_segment_functions()]; constraints=[maintain_complete_coverage(), preserve_original_intent()]; requirements=[segment_atomicity(), semantic_completeness()]; output={segments:list}}`"
    },
    "4406-b-structural_reorder": {
        "title": "Flow Optimizer",
        "interpretation": "Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as:",
        "transformation": "`{role=flow_engineer; input=[segments:list]; process=[analyze_logical_dependencies(), identify_optimal_sequence(), evaluate_transition_coherence(), validate_flow_improvements()]; constraints=[preserve_all_segments(), maintain_semantic_integrity()]; requirements=[enhanced_logical_progression(), improved_clarity()]; output={reordered_segments:list}}`"
    },

    "4406-c-structural_reorder": {
        "title": "Coherent Synthesizer",
        "interpretation": "Your goal is not to **combine** the segments mechanically, but to **synthesize** them into a cohesive, unified text with enhanced clarity and impact. Execute as:",
        "transformation": "`{role=content_synthesizer; input=[reordered_segments:list, original_text:str]; process=[create_natural_transitions(), harmonize_stylistic_elements(), enhance_structural_coherence(), validate_intent_preservation()]; constraints=[maintain_original_essence(), avoid_artificial_phrasing()]; requirements=[seamless_integration(), amplified_impact()]; output={restructured_text:str}}`    "
    },

    "4406-d-structural_reorder": {
        "title": "Bidirectional Resonator",
        "interpretation": "Your goal is not to **finalize** the restructured text, but to **refine** it through bidirectional resonance between decomposition insights and synthesis outcomes. Execute as:",
        "transformation": "`{role=resonance_optimizer; input=[restructured_text:str, original_text:str]; process=[compare_structural_patterns(), identify_optimization_opportunities(), apply_targeted_enhancements(), validate_transformative_impact()]; constraints=[preserve_core_intent(), maintain_authentic_voice()]; requirements=[enhanced_clarity(), maximized_effectiveness()]; output={optimized_text:str}}`"
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
