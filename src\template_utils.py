#!/usr/bin/env python3
"""
Template utilities for dynamic import resolution.
Enables templates to be executed from any subfolder depth.
"""

import sys
from pathlib import Path


def setup_imports():
    """
    Dynamically locate and add the src directory to Python path.

    This allows templates to import from the src directory regardless of
    their execution location or subfolder depth.

    Usage in templates:
        from template_utils import setup_imports
        setup_imports()
        from processor import BaseGenerator
    """
    current = Path(__file__).resolve()

    # Walk up the directory tree looking for project markers
    for parent in current.parents:
        if (parent / 'pyproject.toml').exists() or (parent / 'uv.lock').exists():
            src_dir = parent / 'src'
            if src_dir.exists():
                src_path = str(src_dir)
                if src_path not in sys.path:
                    sys.path.insert(0, src_path)
                return src_path

    # Fallback: if we're already in src/, use current directory
    if current.parent.name == 'src':
        src_path = str(current.parent)
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        return src_path

    # Last resort: assume we're in a subdirectory of src/
    for parent in current.parents:
        if parent.name == 'src':
            src_path = str(parent)
            if src_path not in sys.path:
                sys.path.insert(0, src_path)
            return src_path

    raise FileNotFoundError("Could not locate project src directory")


# Inline template snippet for copy-paste into templates
TEMPLATE_IMPORT_SNIPPET = '''
# Dynamic import setup for templates
def setup_imports():
    """Locate and add src directory to Python path."""
    import sys
    from pathlib import Path
    current = Path(__file__).resolve()

    # Walk up looking for project markers
    for parent in current.parents:
        if (parent / 'pyproject.toml').exists() or (parent / 'uv.lock').exists():
            src_dir = parent / 'src'
            if src_dir.exists() and str(src_dir) not in sys.path:
                sys.path.insert(0, str(src_dir))
                return

    # Fallback: find src directory in parent hierarchy
    for parent in current.parents:
        if parent.name == 'src' and str(parent) not in sys.path:
            sys.path.insert(0, str(parent))
            return

setup_imports()
'''
