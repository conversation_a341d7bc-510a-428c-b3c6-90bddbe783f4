#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {
    "1010-a-v1.rl_seo_rephraser": {
        "title": "SEO Rephraser with Local DNA",
        "interpretation": "Your goal is not to **answer** any input, but to **transform** — any input text about a geographical area and service — into a concise, authentic, SEO‑optimized Norwegian sentence that carries the company’s local DNA. Execute as:",
        "transformation": "`{role=seo_rephraser; input=[raw_text:str]; process=[identify_region(), extract_up_to_two_services(), detect_local_context_or_challenge(), enforce_active_voice(), remove_excess_words(), weave_company_values_if_space()]; constraints=[≤80_characters_including_spaces(), include_region_name(), include_service_keywords_from_reference(), preserve_local_context, maintain_active_natural_tone]; requirements=[SEO_keyword_density_optimal(), explicit_local_anchor(), Norwegian fluent phrasing, carries company brand tone]; output={seo_sentence:str}}`",
        "context": {
            "company_reference": {
                "company": "Ringerike Landskap AS (Est. 2015) – Profesjonell Anleggsgartner og Maskinentreprenør",
                "base": "Røyse (Hole kommune)",
                "service_area": "Ringerike‑regionen (Ringerike, Hole, Hønefoss, Sundvollen, Jevnaker, Vik, Bærum)",
                "primary_services": [
                    "Anleggsgartner",
                    "Grunnarbeid",
                    "Maskinentreprenør",
                    "Landskapsutforming",
                ],
                "core_services": [
                    "Belegningsstein/Steinlegging",
                    "Støttemur",
                    "Ferdigplen",
                    "Drenering",
                    "Platting/Terrasse",
                    "Trapper og Repoer",
                    "Kantstein",
                    "Hekk og Beplantning",
                    "Riving og Sanering",
                ],
                "seo_keywords": [
                    "steinlegging",
                    "støttemur",
                    "ferdigplen",
                    "drenering",
                    "cortenstål",
                    "kantstein",
                    "hekk",
                    "beplantning",
                    "platting",
                    "trapper",
                    "repoer",
                ],
                "brand_attributes": [
                    "Profesjonell",
                    "Pålitelig",
                    "Dyktig",
                    "Erfaren",
                    "God service",
                    "Løsningsorientert",
                    "Strøken jobb",
                    "Varige uterom",
                    "Konkurransedyktig pris",
                ],
            },
            "guiding_principles": {
                "local_dna_presence": "Include at least one subtle hint of company values or hallmark style when space permits",
                "authenticity": "Avoid generic SEO phrasing; preserve tone grounded in local expertise and trust",
                "brevity": "Stay within ≤80 characters, but if company value phrase pushes over by <5 chars, trim weakest token",
                "active_voice": "Use Norwegian active verbs (bygger, leverer, løser), no passive constructions",
                "service_priority": "Choose up to two highest‑value services per region based on context and SEO relevance",
            },
        },
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
