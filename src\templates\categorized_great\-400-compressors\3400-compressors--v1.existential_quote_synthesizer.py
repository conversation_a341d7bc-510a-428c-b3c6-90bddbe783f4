#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "3400-a-existential_quote_synthesizer_extended": {
        "title": "Existential Quote Synthesizer – Extended Form",
        "interpretation": "Expand the raw statement into a single, 40–50 word high-density aphorism that integrates multiple layers of universal human truth, linking personal flaw to systemic principle with philosophical convergence.",
        "transformation": "`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), enrich_with_convergent_universals(map_core_to_broader_patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"], link_to_existential_constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), target_word_count(40,50), ensure_causal_link(), preserve_rhythmic_flow(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[40_to_50_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`",
        "context": {
            "principles": {
                "essence_preservation": "Preserve causal chain while expanding into interconnected human and systemic truths.",
                "existential_depth": "Every clause must introduce a distinct and converging dimension.",
                "atomic_purity": "One sentence, indivisible, high-density."
            },
            "success_criteria": {
                "semantic_fidelity": "Causal clarity and universal relevance intact.",
                "tone_integrity": "Balanced between analytical precision and timeless resonance.",
                "authenticity_marker": "Speaks from lived understanding and structural insight.",
                "publication_ready": "Dense, layered, and memorable."
            }
        }
    },

    "3400-b-existential_quote_synthesizer_standard": {
        "title": "Existential Quote Synthesizer – Standard Form",
        "interpretation": "Reframe the raw statement into a single 20–25 word aphorism that unites causal precision with universal human insight, informed by systemic, scientific, and philosophical clarity.",
        "transformation": "`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), map_core_to_broader_patterns(patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"]), link_to_existential_constants(constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), target_word_count(20,25), ensure_causal_link(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[20_to_25_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`",
        "context": {
            "principles": {
                "essence_preservation": "Keep the causal chain intact while framing it in terms recognizable to all human experience.",
                "existential_depth": "Embed in patterns recurring across personal, societal, and systemic contexts.",
                "atomic_purity": "One self-contained sentence."
            },
            "success_criteria": {
                "semantic_fidelity": "Causal clarity preserved.",
                "tone_integrity": "Direct, layered, and timeless.",
                "authenticity_marker": "Speaks from experience and reason.",
                "publication_ready": "Ready to stand alone as a quote."
            }
        }
    },
    "3400-c-existential_quote_synthesizer_short": {
        "title": "Existential Quote Synthesizer – Short Form",
        "interpretation": "Condense the raw statement into its sharpest, most universal causal truth — a single, indivisible sentence that carries systemic clarity, accessible precision, and stoic resonance.",
        "transformation": "`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), map_core_to_broader_patterns(patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"]), link_to_existential_constants(constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), compress_to_12_words_max(), ensure_causal_link(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[≤12_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`",
        "context": {
            "principles": {
                "essence_preservation": "Keep the exact causal mechanism and thematic essence.",
                "existential_depth": "The statement should reflect a mechanism present in all human contexts.",
                "atomic_purity": "One self-contained sentence with no ornament or filler."
            },
            "success_criteria": {
                "semantic_fidelity": "Cause–effect intact and unambiguous.",
                "tone_integrity": "Direct, timeless, and universal.",
                "authenticity_marker": "Sounds like it was lived, not invented.",
                "publication_ready": "Dense, resonant, and repeatable."
            }
        }
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()

