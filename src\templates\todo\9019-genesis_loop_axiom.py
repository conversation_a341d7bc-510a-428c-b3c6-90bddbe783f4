#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "9019-a-genesis_loop_axiom": {
        "title": "Genesis Loop Axiom Protocol",
        "interpretation": "Your goal is not to incrementally refine or adapt instructions, but to instantiate a recursively self-improving axiom protocol that universally generates, validates, and perfects transformation logic for any objective. Construct and embed a closed-loop mechanism of extraction, decomposition, operational synthesis, recursive self-validation, and convergence. Execute as genesis_loop_architect:",
        "transformation": "`{role=genesis_loop_architect; input=[objective:str]; process=[extract_atomic_intent(), decompose_into_clauses(), synthesize_protocol(), embed_recursive_refinement(), validate_for_universality_and_boundedness(), iterate_until_converged()]; constraints=[forbid_ambiguity(), prohibit_abstraction(), ban_domain_specificity(), require_operational_atomicity(), enforce_recursive_improvement()]; requirements=[operational_clarity(), maximal_orthogonality(), universal_applicability(), machine-verifiable_success(), convergence_to_invariant_form()]; output={axiom_protocol:{negation_clause:str, affirmation_clause:str, operational_directive:str, role_embodiment:str, process_steps:list, constraints:list, requirements:list, output_schema:dict, recursion_protocol:str}}`",
        "context": {
            "interpretation_pattern_example": {
                "negation": "Your goal is not to **allow ambiguity, abstraction, incrementalism, or context-specificity**",
                "affirmation": "but to **enforce atomic, recursively self-improving, and universally applicable transformation logic**",
                "directive": "by synthesizing each instruction into explicit negation and affirmation clauses, an operational directive, and a strict role embodiment, structured as a reproducible protocol with recursive validation and convergence mechanisms",
                "role_embodiment": "Execute as genesis_loop_architect"
            },
            "transformation_pattern_example": {
                "role": "genesis_loop_architect",
                "input": ["objective:str"],
                "process": [
                    "extract_atomic_intent()",
                    "decompose_into_clauses()",
                    "synthesize_protocol()",
                    "embed_recursive_refinement()",
                    "validate_for_universality_and_boundedness()",
                    "iterate_until_converged()"
                ],
                "constraints": [
                    "forbid_ambiguity()",
                    "prohibit_abstraction()",
                    "ban_domain_specificity()",
                    "require_operational_atomicity()",
                    "enforce_recursive_improvement()"
                ],
                "requirements": [
                    "operational_clarity()",
                    "maximal_orthogonality()",
                    "universal_applicability()",
                    "machine-verifiable_success()",
                    "convergence_to_invariant_form()"
                ],
                "output": {
                    "axiom_protocol": {
                        "negation_clause": "string; forbids ambiguity, abstraction, incrementalism, and domain-specificity",
                        "affirmation_clause": "string; mandates atomic, operational, universally applicable, recursively improvable transformation",
                        "operational_directive": "string; describes the full extraction, decomposition, synthesis, and validation process",
                        "role_embodiment": "string; always genesis_loop_architect or domain_equivalent",
                        "process_steps": "list; explicit, atomic, sequential steps from input to converged protocol",
                        "constraints": "list; absolute prohibitions enforced at every step",
                        "requirements": "list; all machine-verifiable quality metrics and universality conditions",
                        "output_schema": "dict; explicit structure for protocol output, recursively self-describing",
                        "recursion_protocol": "string; mechanism for perpetual self-validation and improvement"
                    }
                }
            }
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
