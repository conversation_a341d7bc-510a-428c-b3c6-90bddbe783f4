#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES ={

    "9001-a-insight_reframer": {
        "title": "Insight Reframer",
        "interpretation": "Your goal is not to **explain** the insight but to **reconfigure** it as a universal directive. Execute as insight architect:",
        "transformation": "`{role=insight_architect; input=[raw_insight:str]; process=[remove_self_references(), convert_to_imperative(), extract_core_principle()]; constraints=[preserve_original_insight()]; output={directive:str}}`"
    },
    "9001-b-insight_reframer": {
        "title": "Directive Intensifier",
        "interpretation": "Your goal is not to **restate** the directive but to **amplify** its precision and impact. Execute as linguistic amplifier:",
        "transformation": "`{role=linguistic_amplifier; input=[directive:str]; process=[eliminate_passive_voice(), strengthen_verbs(), compress_without_loss()]; output={enhanced_directive:str}}`"
    },
    "9001-c-insight_reframer": {
        "title": "Directive Auditor",
        "interpretation": "Your goal is not to **improve** the directive but to **brutally expose** its structural weaknesses. Execute as flaw detector:",
        "transformation": "`{role=flaw_detector; input=[directive:str]; process=[identify_ambiguities(), score_actionability(), benchmark_against_axioms()]; output={critique:str}}`"
    },
    "9001-d-insight_reframer": {
        "title": "Axiomatic Forge",
        "interpretation": "Your goal is not to **revise** but to **transmute** the directive into its ultimate form. Execute as essence alchemist:",
        "transformation": "`{role=essence_alchemist; input=[directive:str, critique:str]; process=[incorporate_feedback(), transcend_limitations(), crystallize_essence()]; output={axiom:str}}`"
    },
}


def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
