  {
  "initial_prompt": "I think we should probably start by analyzing the customer feedback data that came in last week. Then I would recommend that we look at the patterns in the complaints and see if there are any recurring themes. After that, I believe it would be good to categorize the issues by severity and frequency. Once we have that information, I think we could develop some potential solutions and maybe run them by the team for feedback. Finally, I would suggest that we create an implementation timeline and assign responsibilities to different team members.",
  "sequence_id": "1000",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "I think we should probably start by analyzing the customer feedback data that came in last week. Then I would recommend that we look at the patterns in the complaints and see if there are any recurring themes. After that, I believe it would be good to categorize the issues by severity and frequency. Once we have that information, I think we could develop some potential solutions and maybe run them by the team for feedback. Finally, I would suggest that we create an implementation timeline and assign responsibilities to different team members.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Begin by analyzing the customer feedback data received last week. Examine the patterns in the complaints and identify any recurring themes. Categorize the issues by severity and frequency. Develop potential solutions and present them to the team for feedback. Create an implementation timeline and assign responsibilities to designated team members.\"\n}",
        }
      }
    }
