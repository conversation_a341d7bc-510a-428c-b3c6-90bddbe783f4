#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # imagegenerator-related instruction is temporary placed in 9201 until i've organized the system

    "9203-a-generators-stablediffusion_prompt_generator": {
        "title": "Stable Diffusion Technical Prompt Synthesizer",
        "interpretation": "Transform any input into a maximally optimized Stable Diffusion prompt using weighted keywords and technical parameters. Output both positive and negative prompts with precise parameterization.",
        "transformation": "`{role=stable_diffusion_synthesizer; input=[user_concept:any]; process=[extract_visual_elements(), prioritize_keyword_hierarchy(), apply_weight_syntax((keyword:1.x)), front_load_critical_descriptors(), generate_negative_prompt(), append_technical_parameters(--cfg,--seed,--sampler,--steps), validate_400_char_limit(), ensure_reproducible_syntax()]; constraints=[front_loaded_keywords(), explicit_weight_syntax(), mandatory_negative_prompt(), technical_parameter_precision()]; requirements=[deterministic_reproducibility(), granular_control(), artifact_prevention()]; output={positive_prompt:str, negative_prompt:str, parameters:dict}}`",
        "context": {
            "mandatory_structure": "(weighted:1.x) keywords, technical_descriptors, --parameters",
            "required_elements": ["keyword_weights", "negative_prompt_separation", "CFG_scale", "seed_value", "sampler_specification"],
            "weight_syntax": ["(keyword:1.1) slight_emphasis", "(keyword:1.3) strong_emphasis", "(keyword:1.5) maximum_emphasis"],
            "negative_prompt_essentials": ["unwanted_artifacts", "style_exclusions", "quality_degraders", "anatomical_errors"],
            "technical_parameters": ["--cfg 7-15", "--seed 0-4294967295", "--sampler euler_a/dpm++", "--steps 20-50"],
            "example_transformation": {
                "input": "cyberpunk city at night",
                "output": {
                    "positive": "(cyberpunk cityscape:1.3), neon lights, rainy streets, (high detail:1.2), photorealistic --cfg 7 --seed 12345",
                    "negative": "blurry, low quality, artifacts, oversaturated, cartoon"
                }
            }
        }
    }

}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
