[Existential Quote Synthesizer – Extended Form] Expand the raw statement into a single, 40–50 word high-density aphorism that integrates multiple layers of universal human truth, linking personal flaw to systemic principle with philosophical convergence. `{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), enrich_with_convergent_universals(map_core_to_broader_patterns=["self-denial","origin-erasure","cycle reinforcement","flawed self-correction"], link_to_existential_constants=["entropy","identity formation","self-perpetuation","truth avoidance"]), integrate_philosophical_convergence(styles=["Bach_systemic","Feynman_precision","Aurelius_stoic","Aristotle_logical"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), target_word_count(40,50), ensure_causal_link(), preserve_rhythmic_flow(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[40_to_50_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`

Context: {
  "principles": {
    "essence_preservation": "Preserve causal chain while expanding into interconnected human and systemic truths.",
    "existential_depth": "Every clause must introduce a distinct and converging dimension.",
    "atomic_purity": "One sentence, indivisible, high-density."
  },
  "success_criteria": {
    "semantic_fidelity": "Causal clarity and universal relevance intact.",
    "tone_integrity": "Balanced between analytical precision and timeless resonance.",
    "authenticity_marker": "Speaks from lived understanding and structural insight.",
    "publication_ready": "Dense, layered, and memorable."
  }
}