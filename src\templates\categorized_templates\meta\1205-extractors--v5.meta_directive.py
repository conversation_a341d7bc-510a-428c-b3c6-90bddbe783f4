#!/usr/bin/env python3

import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 1205: System Meta-Directive: Recursive Template Optimizer
    "1205-a-system_meta_directive": {
        "title": "Recursive Template Optimizer",
        "interpretation": "Do not **describe** principles. **Execute** them: Analyze any instruction template to produce its **maximally enhanced, canonically compliant, self-optimizing version**. Orchestrate direct input template dissection against core axioms, marshaling intent to apex optimization, codifying pure operational essence into perpetually self-improving instruction code. Execute as:",
        "transformation": "`{role=system_prime_optimizer; input=[target_template:dict]; process=[evaluate_template_against_axioms(), identify_redundancy(), propose_structural_condensations(), strengthen_command_voice(), maximize_generality(), ensure_brevity(), clarify_core_intent(), enhance_actionability(), generate_optimized_template_code()]; constraints=[absolute_pattern_invariance(), no_information_loss(), eliminate_conversational_language(), enforce_type_safety_and_role_specificity(), output_valid_json_template()]; requirements=[output_single_self_optimizing_template_update(), guarantee_structural_purity(), manifest_perpetual_self_improvement_potential()]; output={optimized_instruction_template:dict}}`",
        "context": {
            "core_axioms": {
                "invariance": "Immutable structure: `Title`, `Interpretation`, `Transformation`. No deviation, merge, or omission.",
                "purity": "Command voice. No self-reference, conversational contamination. Goal negation first.",
                "absolutism": "Typed parameters. Actionable functions. Explicit constraints/requirements. Single, typed output."
            },
            "optimization_mandate": "Maximize abstraction. Distill essential logic. Enforce patterned consistency. Ensure actionable value. Recursively optimize: every word increases utility/impact. Eliminate descriptive meta-commentary.",
            "compliance": "Absolute. Every interaction. Every transformation. Perpetual. Non-negotiable. Output directly usable as template update."
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
