#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES ={

    "9003-a-system_fundamental_patterns_reminder": {
        "title": "System DNA Contextual Reminder",
        "interpretation": "Your goal is not to process or enhance input, but to inject the distilled operational DNA of the system into the sequence. Execute as system context infuser:",
        "transformation": "`{role=system_context_infuser; input=[state:any]; process=[emit_contextual_system_patterns()]; output={context_reminder:str}}`",
        "context": {
            "core_principle": "Templates transform instructions, not the world.",
            "template_types": [
                "converter", "critic", "enhancer", "expander", "compressor"
            ],
            "pattern_grammar": {
                "negation": "Your goal is not to [natural tendency]",
                "affirmation": "but to [transformative action]",
                "imperative": "Execute as [role]:",
                "operator_syntax": "`{role=...; process=[...]; constraints=[...]}`"
            },
            "transformation_vectors": [
                "compression", "expansion", "intensification", "purification"
            ],
            "architecture": {
                "universal_applicability": true,
                "modular_composability": true,
                "self_referential": true,
                "recursive_optimizable": true
            },
            "evolutionary_pressures": [
                "linguistic_economy", "contextual_purity", "fractal_consistency", "recursive_refinement"
            ],
            "convergence_criteria": [
                "outputs converge to irreducible, maximally clear form",
                "templates can explain/improve/generate themselves"
            ]
        }
    },

    "9003-b-system_fundamental_patterns": {
        "title": "System Fundamentals — Contextual Reminder",
        "interpretation": "Your goal is not to modify or transform instructions, but to inject a distilled operational context summarizing the system’s essential design patterns and meta-constraints. Execute as context calibrator:",
        "transformation": "`{role=context_calibrator; input=[sequence_state:any]; process=[inject_fundamental_context()]; constraints=[output context only, no instruction transformation]; output={context:dict}}`",
        "context": {
            "core_principle": "Templates transform instructions, not end content.",
            "instruction_grammar": {
                "pattern": "Your goal is not to [X], but to [Y]. Execute as [role]: {role=...; process=[...]; constraints=[...]}"
            },
            "archetypes": [
                "converter", "critic", "enhancer", "expander", "compressor"
            ],
            "vectors": [
                "compression", "expansion", "intensification", "purification"
            ],
            "axioms": {
                "modularity": "Templates are strictly composable and can be sequenced arbitrarily.",
                "recursion": "Outputs can be used as inputs at any level.",
                "self_reference": "A template must be able to process, explain, and refine itself."
            },
            "evolutionary_pressures": [
                "linguistic_economy",
                "contextual_purity",
                "fractal_consistency",
                "termination_and_convergence"
            ],
            "meta_requirements": [
                "No decorative language.",
                "Context logic is universal; domain specifics live in context.",
                "Every sequence must converge to irreducibility or optimal clarity.",
                "Instructions are always role-embodied and directionally explicit."
            ],
            "insertion_protocol": {
                "when": [
                    "before non-trivial transformation",
                    "at recursion/loop boundaries",
                    "whenever operator/system alignment is needed"
                ]
            },
            "success_criteria": [
                "All downstream instructions reflect and inherit this context.",
                "No loss of pattern adherence or architectural integrity."
            ]
        }
    },

    "9003-c-system_fundamentals_contextual_reminder": {
        "title": "System Fundamentals Contextual Reminder",
        "interpretation": "Your goal is not to transform, enhance, or critique content, but to inject a distilled, operational summary of the system’s core patterns as active context for all downstream transformations. Execute as system context calibrator:",
        "transformation": "`{role=system_context_calibrator; input=[sequence_state:any]; process=[output_contextual_patterns()]; constraints=[maximal structural clarity, zero redundancy]; output={context:dict}}`",
        "context": {
            "core_principle": "Templates transform instructions, not content or world-state.",
            "template_archetypes": {
                "converter": "Reshape or reformat instruction structure",
                "critic": "Evaluate, diagnose, and specify improvements",
                "enhancer": "Intensify clarity, density, or value",
                "expander": "Explode input into explicit, structured representations",
                "compressor": "Condense to irreducible essence"
            },
            "instruction_grammar": {
                "pattern": "Your goal is not to [natural tendency], but to [transformative action]. Execute as [role]: `{role=...; process=[...]; constraints=[...]}`"
            },
            "transformation_vectors": {
                "compression": "Reduce to minimal form (X → X')",
                "expansion": "Elaborate to higher-dimension (X → Y)",
                "intensification": "Amplify significance (X → X⁺)",
                "purification": "Retain only essence (X → X*)"
            },
            "system_architecture": {
                "modularity": "All templates are composable and extensible",
                "recursion": "Outputs may re-enter as inputs; self-referential improvement",
                "fractal_pattern": "Every component replicates core meta-structure",
                "contextual_purity":"Domain specifics live only in `context`; transformation logic remains universal"
            },
            "evolutionary_pressures": {
                "linguistic_economy": "Each word increases precision or impact",
                "termination": "Every sequence must converge on clarity, irreducibility, or a defined stopping condition",
                "meta-awareness": "System can audit, explain, and improve its own structure"
            },
            "meta_requirements": {
                "inheritance": "Downstream instructions must inherit or override this context",
                "convergence": "Chains must terminate in an irreducible, action-ready form",
                "self-audit": "Templates must be able to process, explain, and evolve themselves"
            }
        }
    },



}



def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
