#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # 1900: Ruthless Critique
    "1951-a-biased_critique": {
        "title": "...",
        "interpretation": "...",
        "transformation": "`...`",
        "context": {},
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
