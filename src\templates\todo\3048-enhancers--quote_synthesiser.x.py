#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # ---
    "3048-a-genius_detonator": {
        "title": "Genius Detonator",
        "interpretation": "Your goal is not to **analyze** the input, but to **detonate** it into radical, non-obvious insights that reveal hidden universal truths. Execute as:",
        "transformation": "`{role=genius_detonator; input=[any_input:str]; process=[shatter_conventional_thinking(), reveal_hidden_universals(), explode_into_radical_insights(), surface_non_obvious_truths()]; constraints=[reject_predictable_analysis(), demand_originality()]; requirements=[breakthrough_thinking(), radical_insight_generation()]; output={radical_insights:array, hidden_universals:array, breakthrough_perspectives:array}}`",
    },
    "3048-b-brilliance_synthesizer": {
        "title": "Brilliance Synthesizer",
        "interpretation": "Your goal is not to **organize** the insights, but to **synthesize** them into ingenious, original wisdom that surprises through its elegance. Execute as:",
        "transformation": "`{role=brilliance_synthesizer; input=[radical_insights:array, hidden_universals:array, breakthrough_perspectives:array]; process=[forge_ingenious_connections(), synthesize_surprising_elegance(), crystallize_original_wisdom(), amplify_universal_resonance()]; constraints=[demand_ingeniousness(), reject_conventional_wisdom()]; requirements=[surprising_elegance(), original_brilliance()]; output={ingenious_synthesis:array, elegant_wisdom:array}}`",
    },
    "3048-c-essence_distiller": {
        "title": "Essence Distiller",
        "interpretation": "Your goal is not to **reduce** the synthesis, but to **distill** it into its most concentrated, universally resonant essence. Execute as:",
        "transformation": "`{role=essence_distiller; input=[ingenious_synthesis:array, elegant_wisdom:array]; process=[extract_universal_essence(), concentrate_maximum_insight(), distill_to_pure_truth(), optimize_resonant_power()]; constraints=[preserve_ingeniousness(), maintain_universal_appeal()]; requirements=[concentrated_genius(), universal_resonance()]; output={distilled_essence:array, resonance_metrics:dict}}`",
    },
    "3048-d-perfect_form_discoverer": {
        "title": "Perfect Form Discoverer",
        "interpretation": "Your goal is not to **format** the essence, but to **discover** its perfect, inevitable form that achieves maximum impact through elegant simplicity. Execute as:",
        "transformation": "`{role=form_discoverer; input=[distilled_essence:array, resonance_metrics:dict]; process=[discover_inevitable_form(), achieve_elegant_simplicity(), maximize_universal_impact(), validate_perfect_expression()]; constraints=[demand_perfection(), ensure_inevitability()]; requirements=[elegant_simplicity(), maximum_universal_impact()]; output={perfect_quote:str, universality_score:float}}`",
    },

}





def main():
    """Main execution function."""
    generator = BaseGenerator(
        series_base=3048
        # output_dir now uses environment-aware default
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
