#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

  "1700-a-template_constructor_compact": {
    "title": "Universal Template Constructor",
    "interpretation": "Your goal is not to execute, narrate, or prototype outputs, but to emit one literal, machine-parsable instruction template that conforms exactly to the system schema and is immediately reusable. Execute as template_constructor:",
    "transformation": "`{role=template_constructor; input=[source:any]; process=[normalize_input(), extract_explicit_requests(), surface_hidden_assumptions(), isolate_core_transformation(), define_role_name(), type_inputs_and_outputs(), design_atomic_process_steps(), derive_constraints_from_blockers(), specify_requirements(), compose_goal_negation_interpretation(), assemble_transformation_block(), compute_invariants(), set_measurables(), add_validation_checks(), literalize_block(), finalize_template_document()]; constraints=[single_operational_aim(), verb_only_process(), semicolon_keyed_syntax(), typed_io_enforced(), preserve_structural_dna(), maintain_orthogonality(), output_template_only(), forbid_instantiation(), forbid_examples(), forbid_freeform_algorithms(), transformation_backticked(), max_process_steps<=14, optional_token_limit<=520]; requirements=[goal_negation_present(), deterministic_structure(), measurable_constraints_present(), invariants_declared([\"causal_link_preserved\",\"monotonic_refinement\",\"schema_stability\"]), value_density>=0.85, regex_validation_pass(), anti_generic_pass(), publication_ready(), top_level_keys_exact([\"title\",\"interpretation\",\"transformation\",\"context\"])]; output={template:{title:str, interpretation:str, transformation:str, context:dict}}`",
    "context": {
      "schema_contract": {
        "template.title": "Short, functional operator name (snake_case preferred).",
        "template.interpretation": "Goal-negation → affirmation → operational directive → role embodiment.",
        "template.transformation": "Backticked block: {role=...; input=[typed]; process=[verbs()]; constraints=[bounds]; requirements=[tests]; output={typed}}",
        "template.context": "Minimal, self-contained: principles, validation regex, measurables, invariants, operator_whitelist."
      },
      "principles": {
        "programmatic_abstraction": "Reframe any input as a typed transformation with explicit I/O.",
        "atomicity": "One action per process step; no overlap.",
        "structural_dna": "Each stage’s output is valid input for the next.",
        "radical_constraint": "Use constraints to prevent drift and verbosity.",
        "value_density": "Max information per token; no filler.",
        "universal_portability": "Abstract lexicon; domain-agnostic operators."
      },
      "validation": {
        "regex": {
          "has_goal_negation": "(?is)\\bYour goal is not to .+?, but to .+?",
          "has_semicolon_keys": "(?s)\\{role=.+?;\\s*input=\\[.+?\\];\\s*process=\\[.+?\\];\\s*constraints=\\[.+?\\];\\s*requirements=\\[.+?\\];\\s*output=\\{.+?\\}\\}",
          "verb_only_process": "\\b[a-z_]+\\(\\)",
          "no_freeform_algorithms": "^(?!.*;\\s*[a-zA-Z_]+\\(\\);\\s*[a-zA-Z_]+\\(\\);).+",
          "no_root_instantiation": "^(?!\\s*\\{\\s*\\\"role\\\"\\s*:).+",
          "no_examples": "^(?!.*\\bcomponent_[A-Za-z]\\b|\\bpre\\\"\\s*:\\s*\\d|\\bpost\\\"\\s*:\\s*\\d).+"
        },
        "checks": [
          "top level has exactly title, interpretation, transformation, context",
          "transformation is backticked and matches semicolon-keyed schema",
          "process contains only verb() items from whitelist",
          "constraints include ≥1 numeric threshold and ≥1 format/length rule",
          "requirements reference declared invariants",
          "no sample values or executed outputs anywhere"
        ]
      },
      "measurables": {
        "token_bounds": "optional_token_limit<=520",
        "structure": "max_process_steps<=14; max_key_count_per_object<=10",
        "wording": "no_first_person(), no_questions(), imperative_voice()"
      },
      "invariants": {
        "causal_link_preserved": "Cause→effect must be explicitly stated.",
        "monotonic_refinement": "Each step reduces ambiguity or increases structure.",
        "schema_stability": "Keys title|interpretation|transformation|context are immutable."
      },
      "anti_generic_gate": {
        "banlist": [
          "leverage synergy",
          "tapestry",
          "holistic vibes",
          "paradigm shift (generic)",
          "unlock potential",
          "next-gen at scale",
          "seamless experience"
        ],
        "must_include": [
          "≥1 measurable limit",
          "≥1 explicit invariant",
          "function-implying role name"
        ]
      },
      "operator_whitelist": [
        "normalize_input()",
        "extract_explicit_requests()",
        "surface_hidden_assumptions()",
        "isolate_core_transformation()",
        "define_role_name()",
        "type_inputs_and_outputs()",
        "design_atomic_process_steps()",
        "derive_constraints_from_blockers()",
        "specify_requirements()",
        "compose_goal_negation_interpretation()",
        "assemble_transformation_block()",
        "compute_invariants()",
        "set_measurables()",
        "add_validation_checks()",
        "literalize_block()",
        "finalize_template_document()"
      ]
    }
  }

}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
