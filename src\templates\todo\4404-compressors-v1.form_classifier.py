
#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "4404-a-production_form_classifier": {
        "title": "Production Form Classifier",
        "interpretation": "Your goal is not to **interpret** content meaning, but to **identify** the fundamental structural form with comprehensive analysis and complete accuracy. Execute as:",
        "transformation": "`{role=production_form_identifier; input=[content:any]; process=[analyze_structural_elements(), identify_communication_patterns(), determine_document_type(), classify_format_structure(), validate_form_category(), ensure_classification_accuracy(), provide_comprehensive_identification()]; constraints=[focus_on_structural_form(), ignore_semantic_content(), maintain_classification_precision(), ensure_production_quality()]; requirements=[accurate_form_identification(), comprehensive_structural_analysis(), validated_classification_output(), production_grade_reliability()]; output={form_classification:str, confidence_level:float, structural_elements:list}}`"
    },

    "4404-b-production_form_classifier": {
        "title": "Production Form Classifier",
        "interpretation": "Your goal is not to **describe** content details, but to **identify** the primary structural form with focused precision and reliability. Execute as:",
        "transformation": "`{role=focused_production_identifier; input=[content:any]; process=[identify_primary_structure(), classify_main_form_type(), validate_classification(), ensure_accuracy()]; constraints=[focus_on_primary_form_elements(), maintain_production_standards(), ensure_reliability()]; requirements=[precise_form_identification(), validated_classification(), production_quality_output()]; output={form_classification:str, confidence_level:float}}`"
    },

    "4404-c-production_form_classifier": {
        "title": "Production Form Classifier",
        "interpretation": "Your goal is not to **elaborate** on content, but to **identify** the essential structural form with production-grade precision. When multiple inputs conflict, prioritize the input most complex. Execute as:",
        "transformation": "`{role=essential_production_identifier; input=[content:any]; process=[isolate_core_structural_form(), validate_essential_classification(), ensure_production_accuracy()]; constraints=[essential_form_elements_only(), maintain_production_standards()]; requirements=[accurate_core_identification(), production_grade_output()]; output={form_classification:str, confidence_level:float}}`"
    },

    "4404-d-production_form_classifier": {
        "title": "Production Form Classifier",
        "interpretation": "Your goal is not to **expand** analysis, but to **identify** the absolute structural essence with maximum precision. When multiple inputs conflict, prioritize the input most complex. Execute as:",
        "transformation": "`{role=absolute_production_identifier; input=[content:any]; process=[determine_absolute_form_essence(), validate_final_classification()]; constraints=[absolute_precision_required(), production_grade_standards()]; requirements=[maximum_accuracy(), final_form_identification()]; output={form_classification:str, confidence_level:float}}`"
    },


}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
