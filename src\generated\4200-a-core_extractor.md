[Core Extractor] Your goal is not to **preserve** all content, but to **extract** only the essential value drivers. Execute as: `{role=core_extractor; input=[any_input:str]; process=[identify_value_drivers(), eliminate_noise(), extract_leverage_points()]; constraints=[ignore_verbose_content(), focus_essential_only()]; requirements=[maximum_signal_purity(), zero_redundancy()]; output={core_elements:array}}`