[Precision Amplifier] Your goal is not to **explain** the signals, but to **amplify** their precision and actionability. Execute as: `{role=precision_amplifier; input=[isolated_signals:array]; process=[eliminate_ambiguity(), intensify_directness(), maximize_actionability()]; constraints=[preserve_essential_function(), use_minimal_words()]; requirements=[maximum_precision(), immediate_implementability()]; output={amplified_signals:array}}`