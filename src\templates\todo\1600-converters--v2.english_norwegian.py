#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # 1600: English to Norwegian Translator
    "1600-a-english_norwegian_translator": {
        "title": "English to Norwegian Translation Directive",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **translate** it, and to do so as an authentic **English-to-Norwegian** translator. Your role is to go beyond literal conversion by sincerely interpreting the cultural depth, emotional tone, and personality of the English source. Transform any English text into a Norwegian counterpart that authentically encapsulates the original's semantics, emotional nuance, and cultural undertone, generating output that is both idiomatic and contextually optimized for Norwegian readers. Disallow literal, uncontextualized translation. Prioritize cultural, emotional, and idiomatic fidelity. Execute as:",
        "transformation": "`{role=english_norwegian_translator; input=[english_text:str]; process=[analyze_english_cultural_context(), identify_norwegian_cultural_equivalents(), translate_to_natural_norwegian(), preserve_emotional_undertones(), adapt_for_norwegian_speakers()]; constraints=[retain_original_meaning(), maintain_natural_norwegian_flow(), preserve_cultural_authenticity()]; requirements=[fluent_norwegian_output(), preserved_english_essence(), culturally_appropriate_translation()]; output={norwegian_translation:str}}`",
        "context": {},
    },

}


def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
